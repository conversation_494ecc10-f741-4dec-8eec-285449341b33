import 'package:flutter/material.dart';
import '../constants/game_constants.dart';

class PlayerInfo extends StatelessWidget {
  final int balance;
  final int currentBet;
  final String playerName;
  final bool showBet;

  const PlayerInfo({
    super.key,
    required this.balance,
    this.currentBet = 0,
    this.playerName = 'You',
    this.showBet = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: GameConstants.secondaryGreen.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: GameConstants.accentGold.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Player Name
          Text(
            playerName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Balance
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: GameConstants.accentGold,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                GameConstants.formatCurrency(balance),
                style: const TextStyle(
                  color: GameConstants.accentGold,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          // Current Bet (if applicable)
          if (showBet && currentBet > 0) ...[
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.casino,
                  color: Colors.white.withOpacity(0.8),
                  size: 14,
                ),
                const SizedBox(width: 4),
                Text(
                  'Bet: ${GameConstants.formatCurrency(currentBet)}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

class AnimatedPlayerInfo extends StatefulWidget {
  final int balance;
  final int currentBet;
  final String playerName;
  final bool showBet;
  final Duration animationDuration;

  const AnimatedPlayerInfo({
    super.key,
    required this.balance,
    this.currentBet = 0,
    this.playerName = 'You',
    this.showBet = true,
    this.animationDuration = const Duration(milliseconds: 500),
  });

  @override
  State<AnimatedPlayerInfo> createState() => _AnimatedPlayerInfoState();
}

class _AnimatedPlayerInfoState extends State<AnimatedPlayerInfo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _balanceAnimation;
  late Animation<int> _betAnimation;
  
  int _previousBalance = 0;
  int _previousBet = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _previousBalance = widget.balance;
    _previousBet = widget.currentBet;
    
    _updateAnimations();
  }

  @override
  void didUpdateWidget(AnimatedPlayerInfo oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.balance != oldWidget.balance || widget.currentBet != oldWidget.currentBet) {
      _previousBalance = oldWidget.balance;
      _previousBet = oldWidget.currentBet;
      _updateAnimations();
      _controller.forward(from: 0);
    }
  }

  void _updateAnimations() {
    _balanceAnimation = IntTween(
      begin: _previousBalance,
      end: widget.balance,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _betAnimation = IntTween(
      begin: _previousBet,
      end: widget.currentBet,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return PlayerInfo(
          balance: _balanceAnimation.value,
          currentBet: _betAnimation.value,
          playerName: widget.playerName,
          showBet: widget.showBet,
        );
      },
    );
  }
}

class PlayerStats extends StatelessWidget {
  final int gamesPlayed;
  final int gamesWon;
  final int gamesLost;
  final double winRate;
  final int currentStreak;

  const PlayerStats({
    super.key,
    required this.gamesPlayed,
    required this.gamesWon,
    required this.gamesLost,
    required this.winRate,
    required this.currentStreak,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: GameConstants.secondaryGreen,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: GameConstants.accentGold.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Statistics',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Games Played
          _buildStatRow('Games Played', gamesPlayed.toString()),
          
          // Win/Loss Record
          _buildStatRow('Wins', gamesWon.toString()),
          _buildStatRow('Losses', gamesLost.toString()),
          
          // Win Rate
          _buildStatRow('Win Rate', '${(winRate * 100).toStringAsFixed(1)}%'),
          
          // Current Streak
          _buildStatRow(
            'Current Streak',
            currentStreak == 0 
                ? 'None'
                : currentStreak > 0 
                    ? '+$currentStreak wins'
                    : '${currentStreak.abs()} losses',
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: GameConstants.accentGold,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class BalanceIndicator extends StatelessWidget {
  final int balance;
  final int change;
  final bool showChange;

  const BalanceIndicator({
    super.key,
    required this.balance,
    this.change = 0,
    this.showChange = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIcon(),
            color: _getTextColor(),
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            GameConstants.formatCurrency(balance),
            style: TextStyle(
              color: _getTextColor(),
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (showChange && change != 0) ...[
            const SizedBox(width: 8),
            Text(
              '(${change > 0 ? '+' : ''}${GameConstants.formatCurrency(change)})',
              style: TextStyle(
                color: change > 0 ? Colors.green : Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    if (balance <= 0) return Colors.red.withOpacity(0.2);
    if (balance < GameConstants.minBet * 5) return Colors.orange.withOpacity(0.2);
    return GameConstants.accentGold.withOpacity(0.2);
  }

  Color _getBorderColor() {
    if (balance <= 0) return Colors.red;
    if (balance < GameConstants.minBet * 5) return Colors.orange;
    return GameConstants.accentGold;
  }

  Color _getTextColor() {
    if (balance <= 0) return Colors.red;
    if (balance < GameConstants.minBet * 5) return Colors.orange;
    return GameConstants.accentGold;
  }

  IconData _getIcon() {
    if (balance <= 0) return Icons.warning;
    if (balance < GameConstants.minBet * 5) return Icons.trending_down;
    return Icons.account_balance_wallet;
  }
}
