import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../constants/game_constants.dart';

class EnhancedChipTray extends StatefulWidget {
  final List<int> availableChips;
  final int playerBalance;
  final int? selectedChip;
  final Function(int) onChipSelected;
  final bool enabled;

  const EnhancedChipTray({
    super.key,
    required this.availableChips,
    required this.playerBalance,
    this.selectedChip,
    required this.onChipSelected,
    this.enabled = true,
  });

  @override
  State<EnhancedChipTray> createState() => _EnhancedChipTrayState();
}

class _EnhancedChipTrayState extends State<EnhancedChipTray>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _rotationAnimations;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.availableChips.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers.map((controller) =>
      Tween<double>(begin: 1.0, end: 1.2).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      ),
    ).toList();

    _rotationAnimations = _controllers.map((controller) =>
      Tween<double>(begin: 0.0, end: 0.1).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      ),
    ).toList();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _animateChip(int index) {
    _controllers[index].forward().then((_) {
      _controllers[index].reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.8),
            Colors.black.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: GameConstants.accentGold, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.5),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          Text(
            'Select Chip',
            style: TextStyle(
              color: GameConstants.accentGold,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Chips Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: widget.availableChips.asMap().entries.map((entry) {
              final index = entry.key;
              final chipValue = entry.value;
              final isSelected = widget.selectedChip == chipValue;
              final canAfford = chipValue <= widget.playerBalance;
              
              return AnimatedBuilder(
                animation: Listenable.merge([
                  _scaleAnimations[index],
                  _rotationAnimations[index],
                ]),
                builder: (context, child) {
                  return Transform.scale(
                    scale: isSelected ? 1.1 : _scaleAnimations[index].value,
                    child: Transform.rotate(
                      angle: _rotationAnimations[index].value,
                      child: GestureDetector(
                        onTap: widget.enabled && canAfford ? () {
                          _animateChip(index);
                          widget.onChipSelected(chipValue);
                        } : null,
                        child: _buildChip(chipValue, isSelected, canAfford),
                      ),
                    ),
                  );
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 12),
          
          // Balance Display
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: GameConstants.accentGold, width: 1),
            ),
            child: Text(
              'Balance: ${GameConstants.formatCurrency(widget.playerBalance)}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChip(int value, bool isSelected, bool canAfford) {
    final chipColor = _getChipColor(value);
    final textColor = _getTextColor(value);
    
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: const Alignment(-0.3, -0.3),
          radius: 0.8,
          colors: canAfford ? [
            chipColor.withValues(alpha: 1.0),
            chipColor.withValues(alpha: 0.7),
            chipColor.withValues(alpha: 0.9),
          ] : [
            Colors.grey.withValues(alpha: 0.5),
            Colors.grey.withValues(alpha: 0.3),
            Colors.grey.withValues(alpha: 0.4),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: isSelected 
              ? GameConstants.accentGold 
              : (canAfford ? Colors.white : Colors.grey),
          width: isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected 
                ? GameConstants.accentGold.withValues(alpha: 0.5)
                : Colors.black.withValues(alpha: 0.3),
            blurRadius: isSelected ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Inner circle for depth
          Center(
            child: Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: canAfford 
                    ? chipColor.withValues(alpha: 0.8)
                    : Colors.grey.withValues(alpha: 0.4),
                border: Border.all(
                  color: canAfford ? Colors.white.withValues(alpha: 0.3) : Colors.grey,
                  width: 1,
                ),
              ),
            ),
          ),
          
          // Value text
          Center(
            child: Text(
              GameConstants.formatCurrency(value),
              style: TextStyle(
                color: canAfford ? textColor : Colors.grey.shade600,
                fontSize: value >= 1000 ? 10 : 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // Disabled overlay
          if (!canAfford)
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withValues(alpha: 0.5),
              ),
              child: const Center(
                child: Icon(
                  Icons.block,
                  color: Colors.red,
                  size: 20,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getChipColor(int value) {
    switch (value) {
      case 5: return const Color(0xFFE53E3E);    // Red
      case 10: return const Color(0xFF3182CE);   // Blue
      case 25: return const Color(0xFF38A169);   // Green
      case 50: return const Color(0xFF805AD5);   // Purple
      case 100: return const Color(0xFF000000);  // Black
      case 500: return const Color(0xFFD69E2E);  // Orange
      case 1000: return const Color(0xFFE53E3E); // Dark Red
      default: return Colors.grey;
    }
  }

  Color _getTextColor(int value) {
    switch (value) {
      case 100: return Colors.white;
      default: return Colors.white;
    }
  }
}
