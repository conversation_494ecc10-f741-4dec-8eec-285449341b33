import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/card.dart';
import '../constants/game_constants.dart';
import 'card_widget.dart';
import 'chip_widget.dart';

class DealingAnimation extends StatefulWidget {
  final List<PlayingCard> cards;
  final List<Offset> targetPositions;
  final Offset deckPosition;
  final Duration dealInterval;
  final VoidCallback? onComplete;
  final VoidCallback? onCardDealt;

  const DealingAnimation({
    super.key,
    required this.cards,
    required this.targetPositions,
    required this.deckPosition,
    this.dealInterval = const Duration(milliseconds: 400),
    this.onComplete,
    this.onCardDealt,
  });

  @override
  State<DealingAnimation> createState() => _DealingAnimationState();
}

class _DealingAnimationState extends State<DealingAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _progressAnimations;
  late List<Animation<double>> _rotationYAnimations;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _opacityAnimations;

  int _currentCardIndex = 0;
  bool _isDealing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startDealing();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.cards.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 500), // 复制原版cardDeal时长
        vsync: this,
      ),
    );

    // 复制原版cardDeal动画的三个阶段
    _progressAnimations = _controllers.map((controller) =>
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOut),
      ),
    ).toList();

    // 3D翻转效果 - rotateY(180deg) → rotateY(90deg) → rotateY(0deg)
    _rotationYAnimations = _controllers.map((controller) =>
      TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 3.14159, end: 1.5708), // 180° → 90°
          weight: 0.5,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.5708, end: 0.0), // 90° → 0°
          weight: 0.5,
        ),
      ]).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      )),
    ).toList();

    // 缩放动画 - scale(0.8) → scale(0.9) → scale(1)
    _scaleAnimations = _controllers.map((controller) =>
      TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.8, end: 0.9),
          weight: 0.5,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.9, end: 1.0),
          weight: 0.5,
        ),
      ]).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      )),
    ).toList();

    // 透明度动画 - opacity(0) → opacity(0.5) → opacity(1)
    _opacityAnimations = _controllers.map((controller) =>
      TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.0, end: 0.5),
          weight: 0.5,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.5, end: 1.0),
          weight: 0.5,
        ),
      ]).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      )),
    ).toList();
  }

  // 计算卡牌位置 - 从牌堆到目标位置
  Offset _calculateCardPosition(int index, double progress) {
    final targetIndex = index % widget.targetPositions.length;
    final targetPosition = widget.targetPositions[targetIndex];

    // Y轴动画：translateY(-30px) → translateY(-5px) → translateY(0)
    double yOffset;
    if (progress <= 0.5) {
      yOffset = -30.0 + (-5.0 - (-30.0)) * (progress * 2);
    } else {
      yOffset = -5.0 + (0.0 - (-5.0)) * ((progress - 0.5) * 2);
    }

    return Offset(
      widget.deckPosition.dx + (targetPosition.dx - widget.deckPosition.dx) * progress,
      widget.deckPosition.dy + (targetPosition.dy - widget.deckPosition.dy) * progress + yOffset,
    );
  }

  Future<void> _startDealing() async {
    setState(() {
      _isDealing = true;
    });

    for (int i = 0; i < widget.cards.length; i++) {
      if (mounted) {
        setState(() {
          _currentCardIndex = i;
        });

        _controllers[i].forward();
        widget.onCardDealt?.call(); // 触发发牌音效

        if (i < widget.cards.length - 1) {
          await Future.delayed(widget.dealInterval);
        }
      }
    }

    // 等待最后一张卡牌动画完成
    await _controllers.last.forward();

    if (mounted) {
      setState(() {
        _isDealing = false;
      });
      widget.onComplete?.call();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 渲染已开始动画的卡牌
        for (int i = 0; i <= _currentCardIndex && i < widget.cards.length; i++)
          AnimatedBuilder(
            animation: _controllers[i],
            builder: (context, child) {
              final position = _calculateCardPosition(i, _progressAnimations[i].value);

              return Positioned(
                left: position.dx,
                top: position.dy,
                child: Opacity(
                  opacity: _opacityAnimations[i].value,
                  child: Transform.scale(
                    scale: _scaleAnimations[i].value,
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.001) // 透视效果
                        ..rotateY(_rotationYAnimations[i].value),
                      child: CardWidget(
                        card: widget.cards[i],
                        isDealing: true,
                        showAnimation: false, // 由DealingAnimation控制动画
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}

class ChipFlyAnimation extends StatefulWidget {
  final int chipValue;
  final Offset startPosition;
  final Offset endPosition;
  final Duration duration;
  final VoidCallback? onComplete;
  final bool isWinning;

  const ChipFlyAnimation({
    super.key,
    required this.chipValue,
    required this.startPosition,
    required this.endPosition,
    this.duration = const Duration(milliseconds: 800),
    this.onComplete,
    this.isWinning = false,
  });

  @override
  State<ChipFlyAnimation> createState() => _ChipFlyAnimationState();
}

class _ChipFlyAnimationState extends State<ChipFlyAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutCubic,
    ));

    // 复制原版的旋转效果 - 360度旋转
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 6.28, // 360度 = 2π
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    // 复制原版的缩放效果
    if (widget.isWinning) {
      // 胜利筹码动画
      _scaleAnimation = TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.8, end: 1.2),
          weight: 0.2,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.2, end: 1.0),
          weight: 0.8,
        ),
      ]).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      _opacityAnimation = TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          weight: 0.2,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.0, end: 1.0),
          weight: 0.8,
        ),
      ]).animate(_controller);
    } else {
      // 下注筹码动画
      _scaleAnimation = TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.0, end: 1.2),
          weight: 0.5,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.2, end: 0.8),
          weight: 0.5,
        ),
      ]).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      _opacityAnimation = TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.0, end: 0.9),
          weight: 0.5,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.9, end: 0.0),
          weight: 0.5,
        ),
      ]).animate(_controller);
    }

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onComplete?.call();
      }
    });

    _controller.forward();
  }

  // 计算抛物线轨迹 - 复制原版CSS的calc公式
  Offset _calculatePosition(double progress) {
    final startX = widget.startPosition.dx;
    final startY = widget.startPosition.dy;
    final endX = widget.endPosition.dx;
    final endY = widget.endPosition.dy;

    // 中点上升50px，复制原版的抛物线效果
    final midX = (startX + endX) / 2;
    final midY = (startY + endY) / 2 - 50;

    double x, y;

    if (progress <= 0.5) {
      // 前半段：从起点到中点
      final t = progress * 2;
      x = startX + (midX - startX) * t;
      y = startY + (midY - startY) * t;
    } else {
      // 后半段：从中点到终点
      final t = (progress - 0.5) * 2;
      x = midX + (endX - midX) * t;
      y = midY + (endY - midY) * t;
    }

    return Offset(x, y);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final position = _calculatePosition(_progressAnimation.value);

        return Positioned(
          left: position.dx - 20, // 居中筹码
          top: position.dy - 20,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: GameConstants.accentGold.withOpacity(0.6),
                        blurRadius: 12,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: ChipWidget(value: widget.chipValue, size: 40),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class WinningAnimation extends StatefulWidget {
  final Widget child;
  final bool isWinning;
  final Duration duration;

  const WinningAnimation({
    super.key,
    required this.child,
    required this.isWinning,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<WinningAnimation> createState() => _WinningAnimationState();
}

class _WinningAnimationState extends State<WinningAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _glowAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.1),
        weight: 0.3,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.0),
        weight: 0.7,
      ),
    ]).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    if (widget.isWinning) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(WinningAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isWinning != oldWidget.isWinning) {
      if (widget.isWinning) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isWinning) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: GameConstants.accentGold.withOpacity(_glowAnimation.value * 0.6),
                  blurRadius: 20 * _glowAnimation.value,
                  spreadRadius: 5 * _glowAnimation.value,
                ),
              ],
            ),
            child: widget.child,
          ),
        );
      },
    );
  }
}
