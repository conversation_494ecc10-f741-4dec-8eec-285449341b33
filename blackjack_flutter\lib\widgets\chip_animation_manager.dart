import 'package:flutter/material.dart';
import '../constants/game_constants.dart';
import 'chip_widget.dart';
import 'enhanced_card_animations.dart';

class ChipAnimationManager extends StatefulWidget {
  final Widget child;
  final GlobalKey chipTrayKey;
  final GlobalKey bettingAreaKey;

  const ChipAnimationManager({
    super.key,
    required this.child,
    required this.chipTrayKey,
    required this.bettingAreaKey,
  });

  @override
  State<ChipAnimationManager> createState() => _ChipAnimationManagerState();
}

class _ChipAnimationManagerState extends State<ChipAnimationManager> {
  final List<Widget> _activeAnimations = [];

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        ..._activeAnimations,
      ],
    );
  }

  void animateChipToBet(int chipValue) {
    final chipTrayBox = widget.chipTrayKey.currentContext?.findRenderObject() as RenderBox?;
    final bettingAreaBox = widget.bettingAreaKey.currentContext?.findRenderObject() as RenderBox?;

    if (chipTrayBox == null || bettingAreaBox == null) return;

    final chipTrayPosition = chipTrayBox.localToGlobal(Offset.zero);
    final bettingAreaPosition = bettingAreaBox.localToGlobal(Offset.zero);

    // 计算筹码在托盘中的精确位置
    final chipIndex = _getChipIndex(chipValue);
    final startPosition = Offset(
      chipTrayPosition.dx + (chipIndex * 50.0) + 25,
      chipTrayPosition.dy + 25,
    );

    // 下注区域中心位置
    final endPosition = Offset(
      bettingAreaPosition.dx + 40,
      bettingAreaPosition.dy + 40,
    );

    // 创建复制原版效果的筹码飞行动画
    late ChipFlyAnimation animationWidget;
    animationWidget = ChipFlyAnimation(
      chipValue: chipValue,
      startPosition: startPosition,
      endPosition: endPosition,
      duration: const Duration(milliseconds: 800), // 复制原版时长
      isWinning: false,
      onComplete: () {
        setState(() {
          _activeAnimations.remove(animationWidget);
        });
        // 播放筹码放置音效
        // AudioService().playSound('chip');
      },
    );

    setState(() {
      _activeAnimations.add(animationWidget);
    });
  }

  void animateChipReturn(int chipValue) {
    final chipTrayBox = widget.chipTrayKey.currentContext?.findRenderObject() as RenderBox?;
    final bettingAreaBox = widget.bettingAreaKey.currentContext?.findRenderObject() as RenderBox?;
    
    if (chipTrayBox == null || bettingAreaBox == null) return;

    final chipTrayPosition = chipTrayBox.localToGlobal(Offset.zero);
    final bettingAreaPosition = bettingAreaBox.localToGlobal(Offset.zero);
    
    final chipIndex = _getChipIndex(chipValue);
    final startPosition = Offset(
      bettingAreaPosition.dx + 40,
      bettingAreaPosition.dy + 40,
    );
    
    final endPosition = Offset(
      chipTrayPosition.dx + (chipIndex * 50.0) + 25,
      chipTrayPosition.dy + 25,
    );

    final animationWidget = ChipFlyAnimation(
      chipValue: chipValue,
      startPosition: startPosition,
      endPosition: endPosition,
      duration: const Duration(milliseconds: 400),
      onComplete: () {
        setState(() {
          _activeAnimations.removeWhere((widget) => widget is ChipFlyAnimation);
        });
      },
    );

    setState(() {
      _activeAnimations.add(animationWidget);
    });
  }

  int _getChipIndex(int value) {
    final chipValues = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
    return chipValues.indexOf(value).clamp(0, chipValues.length - 1);
  }
}

class AnimatedChipTray extends StatefulWidget {
  final List<int> availableChips;
  final int playerBalance;
  final Function(int) onChipSelected;
  final int? selectedChip;

  const AnimatedChipTray({
    super.key,
    required this.availableChips,
    required this.playerBalance,
    required this.onChipSelected,
    this.selectedChip,
  });

  @override
  State<AnimatedChipTray> createState() => _AnimatedChipTrayState();
}

class _AnimatedChipTrayState extends State<AnimatedChipTray>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _positionAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startEntryAnimation();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.availableChips.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + (index * 100)),
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers.map((controller) =>
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      ),
    ).toList();

    _positionAnimations = _controllers.asMap().entries.map((entry) {
      final index = entry.key;
      final controller = entry.value;
      return Tween<Offset>(
        begin: Offset(0, 50),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutBack,
      ));
    }).toList();
  }

  Future<void> _startEntryAnimation() async {
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].forward();
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: GameConstants.accentGold.withOpacity(0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: widget.availableChips.asMap().entries.map((entry) {
          final index = entry.key;
          final chipValue = entry.value;
          final isSelected = widget.selectedChip == chipValue;
          final canAfford = widget.playerBalance >= chipValue;

          if (index >= _controllers.length) {
            return const SizedBox.shrink();
          }

          return AnimatedBuilder(
            animation: _controllers[index],
            builder: (context, child) {
              return Transform.translate(
                offset: _positionAnimations[index].value,
                child: Transform.scale(
                  scale: _scaleAnimations[index].value,
                  child: GestureDetector(
                    onTap: canAfford ? () {
                      widget.onChipSelected(chipValue);
                      _animateChipSelection(index);
                    } : null,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      transform: Matrix4.identity()
                        ..scale(isSelected ? 1.1 : 1.0)
                        ..translate(0.0, isSelected ? -3.0 : 0.0),
                      child: Opacity(
                        opacity: canAfford ? 1.0 : 0.4,
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: GameConstants.accentGold.withOpacity(0.6),
                                blurRadius: 15,
                                spreadRadius: 3,
                              ),
                            ] : null,
                          ),
                          child: ChipWidget(
                            value: chipValue,
                            size: 43,
                            isSelected: isSelected,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }).toList(),
      ),
    );
  }

  void _animateChipSelection(int index) {
    if (index < _controllers.length) {
      _controllers[index].reverse().then((_) {
        _controllers[index].forward();
      });
    }
  }
}

class PulsingBetArea extends StatefulWidget {
  final Widget child;
  final bool isActive;

  const PulsingBetArea({
    super.key,
    required this.child,
    required this.isActive,
  });

  @override
  State<PulsingBetArea> createState() => _PulsingBetAreaState();
}

class _PulsingBetAreaState extends State<PulsingBetArea>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isActive) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PulsingBetArea oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isActive) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: GameConstants.accentGold.withOpacity(0.4),
                  blurRadius: 20 * _pulseAnimation.value,
                  spreadRadius: 5 * _pulseAnimation.value,
                ),
              ],
            ),
            child: widget.child,
          ),
        );
      },
    );
  }
}
