enum Suit { spades, hearts, diamonds, clubs }

enum CardValue { ace, two, three, four, five, six, seven, eight, nine, ten, jack, queen, king }

class PlayingCard {
  final Suit suit;
  final CardValue value;
  final bool isVisible;

  const PlayingCard({
    required this.suit,
    required this.value,
    this.isVisible = true,
  });

  String get suitSymbol {
    switch (suit) {
      case Suit.spades:
        return '♠';
      case Suit.hearts:
        return '♥';
      case Suit.diamonds:
        return '♦';
      case Suit.clubs:
        return '♣';
    }
  }

  String get valueString {
    switch (value) {
      case CardValue.ace:
        return 'A';
      case CardValue.two:
        return '2';
      case CardValue.three:
        return '3';
      case CardValue.four:
        return '4';
      case CardValue.five:
        return '5';
      case CardValue.six:
        return '6';
      case CardValue.seven:
        return '7';
      case CardValue.eight:
        return '8';
      case CardValue.nine:
        return '9';
      case CardValue.ten:
        return '10';
      case CardValue.jack:
        return 'J';
      case CardValue.queen:
        return 'Q';
      case CardValue.king:
        return 'K';
    }
  }

  int get numericValue {
    switch (value) {
      case CardValue.ace:
        return 1; // Ace can be 1 or 11, handled in game logic
      case CardValue.two:
        return 2;
      case CardValue.three:
        return 3;
      case CardValue.four:
        return 4;
      case CardValue.five:
        return 5;
      case CardValue.six:
        return 6;
      case CardValue.seven:
        return 7;
      case CardValue.eight:
        return 8;
      case CardValue.nine:
        return 9;
      case CardValue.ten:
      case CardValue.jack:
      case CardValue.queen:
      case CardValue.king:
        return 10;
    }
  }

  bool get isRed => suit == Suit.hearts || suit == Suit.diamonds;
  bool get isBlack => suit == Suit.spades || suit == Suit.clubs;

  PlayingCard copyWith({
    Suit? suit,
    CardValue? value,
    bool? isVisible,
  }) {
    return PlayingCard(
      suit: suit ?? this.suit,
      value: value ?? this.value,
      isVisible: isVisible ?? this.isVisible,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlayingCard &&
        other.suit == suit &&
        other.value == value;
  }

  @override
  int get hashCode => suit.hashCode ^ value.hashCode;

  @override
  String toString() {
    return '$valueString$suitSymbol';
  }
}

class Deck {
  List<PlayingCard> _cards = [];
  List<PlayingCard> _discardPile = [];

  List<PlayingCard> get cards => List.unmodifiable(_cards);
  List<PlayingCard> get discardPile => List.unmodifiable(_discardPile);
  int get remainingCards => _cards.length;
  int get totalCards => _cards.length + _discardPile.length;

  void createStandardDeck({int deckCount = 1}) {
    _cards.clear();
    _discardPile.clear();

    for (int i = 0; i < deckCount; i++) {
      for (Suit suit in Suit.values) {
        for (CardValue value in CardValue.values) {
          _cards.add(PlayingCard(suit: suit, value: value));
        }
      }
    }
  }

  void shuffle() {
    _cards.shuffle();
  }

  PlayingCard? dealCard() {
    if (_cards.isEmpty) {
      return null;
    }
    final card = _cards.removeAt(0);
    return card;
  }

  void addToDiscardPile(PlayingCard card) {
    _discardPile.add(card);
  }

  void reshuffleDiscardPile() {
    _cards.addAll(_discardPile);
    _discardPile.clear();
    shuffle();
  }

  bool shouldReshuffle({double threshold = 0.25}) {
    return remainingCards < (totalCards * threshold);
  }
}
