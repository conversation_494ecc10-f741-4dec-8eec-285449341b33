import 'package:audioplayers/audioplayers.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _soundPlayer = AudioPlayer();
  final AudioPlayer _musicPlayer = AudioPlayer();
  bool _isInitialized = false;

  Future<void> _initialize() async {
    try {
      await _soundPlayer.setPlayerMode(PlayerMode.lowLatency);
      await _musicPlayer.setPlayerMode(PlayerMode.mediaPlayer);
      _isInitialized = true;
      print('🔊 Audio service initialized successfully');
    } catch (e) {
      print('Audio initialization failed: $e');
      _isInitialized = false;
    }
  }

  Future<void> playSound(String soundType, {double volume = 0.5}) async {
    if (!_isInitialized) {
      await _initialize();
      if (!_isInitialized) return;
    }

    try {
      await _soundPlayer.setVolume(volume);

      String soundPath;
      switch (soundType) {
        case 'deal':
        case 'cardDeal':
        case 'cardFlip':
        case 'hit':
          soundPath = 'sounds/deal.mp3';
          break;
        case 'chip':
        case 'chipPlace':
          soundPath = 'sounds/deal.mp3'; // Using deal sound as placeholder
          break;
        case 'win':
          soundPath = 'sounds/win.mp3';
          break;
        case 'shuffle':
          soundPath = 'sounds/deal.mp3'; // Using deal sound as placeholder
          break;
        case 'button':
          soundPath = 'sounds/deal.mp3'; // Using deal sound as placeholder
          break;
        default:
          print('🔊 Unknown sound type: $soundType');
          return;
      }

      await _soundPlayer.play(AssetSource(soundPath));
      print('🔊 Playing sound: $soundType (${(volume * 100).round()}%)');
    } catch (e) {
      print('Failed to play sound $soundType: $e');
      // Fallback to console output
      _playFallbackSound(soundType, volume);
    }
  }

  void _playFallbackSound(String soundType, double volume) {
    switch (soundType) {
      case 'deal':
        print('🃏 Card deal sound (volume: ${(volume * 100).round()}%)');
        break;
      case 'chip':
        print('🪙 Chip sound (volume: ${(volume * 100).round()}%)');
        break;
      case 'win':
        print('🎉 Win sound (volume: ${(volume * 100).round()}%)');
        break;
      case 'shuffle':
        print('🔀 Shuffle sound (volume: ${(volume * 100).round()}%)');
        break;
      default:
        print('🔊 Generic sound: $soundType (volume: ${(volume * 100).round()}%)');
    }
  }

  Future<void> playBackgroundMusic({double volume = 0.3}) async {
    if (!_isInitialized) {
      await _initialize();
      if (!_isInitialized) return;
    }

    try {
      await _musicPlayer.setVolume(volume);
      await _musicPlayer.setReleaseMode(ReleaseMode.loop);
      await _musicPlayer.play(AssetSource('sounds/backJazz.mp3'));
      print('🎵 Playing background music (volume: ${(volume * 100).round()}%)');
    } catch (e) {
      print('Failed to play background music: $e');
      print('🎵 Background music placeholder (volume: ${(volume * 100).round()}%)');
    }
  }

  Future<void> stopBackgroundMusic() async {
    if (!_isInitialized) return;

    try {
      await _musicPlayer.stop();
      print('🔇 Stopping background music');
    } catch (e) {
      print('Failed to stop background music: $e');
    }
  }

  Future<void> pauseBackgroundMusic() async {
    if (!_isInitialized) return;

    try {
      await _musicPlayer.pause();
      print('⏸️ Pausing background music');
    } catch (e) {
      print('Failed to pause background music: $e');
    }
  }

  Future<void> resumeBackgroundMusic() async {
    if (!_isInitialized) return;

    try {
      await _musicPlayer.resume();
      print('▶️ Resuming background music');
    } catch (e) {
      print('Failed to resume background music: $e');
    }
  }

  Future<void> setMusicVolume(double volume) async {
    if (!_isInitialized) return;

    try {
      await _musicPlayer.setVolume(volume);
      print('🎵 Setting music volume to: ${(volume * 100).round()}%');
    } catch (e) {
      print('Failed to set music volume: $e');
    }
  }

  Future<void> setSoundVolume(double volume) async {
    if (!_isInitialized) return;

    try {
      await _soundPlayer.setVolume(volume);
      print('🔊 Setting sound volume to: ${(volume * 100).round()}%');
    } catch (e) {
      print('Failed to set sound volume: $e');
    }
  }

  void dispose() {
    try {
      _soundPlayer.dispose();
      _musicPlayer.dispose();
      print('🗑️ Disposing audio service');
      _isInitialized = false;
    } catch (e) {
      print('Failed to dispose audio service: $e');
    }
  }
}
