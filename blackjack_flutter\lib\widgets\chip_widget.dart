import 'package:flutter/material.dart';
import '../constants/game_constants.dart';

class ChipWidget extends StatelessWidget {
  final int value;
  final double size;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showValue;

  const ChipWidget({
    super.key,
    required this.value,
    this.size = GameConstants.chipSize,
    this.isSelected = false,
    this.onTap,
    this.showValue = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: size,
        height: size,
        transform: Matrix4.identity()
          ..scale(isSelected ? 1.15 : 1.0)
          ..translate(0.0, isSelected ? -5.0 : 0.0),
        child: _buildChipDesign(),
      ),
    );
  }

  Widget _buildChipDesign() {
    // 精美的筹码设计
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            GameConstants.getChipColor(value).withOpacity(0.9),
            GameConstants.getChipColor(value),
            GameConstants.getChipColor(value).withOpacity(0.7),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: isSelected ? GameConstants.accentGold : Colors.white,
          width: isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 6,
            offset: const Offset(2, 2),
          ),
          if (isSelected)
            BoxShadow(
              color: GameConstants.accentGold.withOpacity(0.5),
              blurRadius: 12,
              spreadRadius: 2,
            ),
        ],
      ),
      child: Stack(
        children: [
          // 内圈装饰
          Center(
            child: Container(
              width: size * 0.6,
              height: size * 0.6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.8),
                  width: 1,
                ),
              ),
            ),
          ),

          // 数值文字
          Center(
            child: Text(
              GameConstants.formatCurrency(value),
              style: TextStyle(
                color: _getTextColor(),
                fontSize: size * 0.2,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.5),
                    offset: const Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTextColor() {
    final chipColor = GameConstants.getChipColor(value);
    final luminance = chipColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}

class AnimatedChipWidget extends StatefulWidget {
  final int value;
  final double size;
  final Duration animationDuration;
  final Offset? startPosition;
  final Offset? endPosition;
  final VoidCallback? onAnimationComplete;

  const AnimatedChipWidget({
    super.key,
    required this.value,
    this.size = GameConstants.chipSize,
    this.animationDuration = GameConstants.chipAnimationDuration,
    this.startPosition,
    this.endPosition,
    this.onAnimationComplete,
  });

  @override
  State<AnimatedChipWidget> createState() => _AnimatedChipWidgetState();
}

class _AnimatedChipWidgetState extends State<AnimatedChipWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _positionAnimation = Tween<Offset>(
      begin: widget.startPosition ?? Offset.zero,
      end: widget.endPosition ?? Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onAnimationComplete?.call();
      }
    });

    // Start animation
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: _positionAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: ChipWidget(
              value: widget.value,
              size: widget.size,
            ),
          ),
        );
      },
    );
  }
}

class ChipStack extends StatelessWidget {
  final List<int> chipValues;
  final double chipSize;
  final double stackOffset;

  const ChipStack({
    super.key,
    required this.chipValues,
    this.chipSize = GameConstants.chipSize,
    this.stackOffset = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    if (chipValues.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      width: chipSize,
      height: chipSize + (chipValues.length - 1) * stackOffset,
      child: Stack(
        children: chipValues.asMap().entries.map((entry) {
          final index = entry.key;
          final value = entry.value;
          
          return Positioned(
            top: index * stackOffset,
            child: ChipWidget(
              value: value,
              size: chipSize,
            ),
          );
        }).toList(),
      ),
    );
  }
}

class ChipSelector extends StatefulWidget {
  final List<int> availableChips;
  final int? selectedChip;
  final Function(int) onChipSelected;
  final int playerBalance;

  const ChipSelector({
    super.key,
    required this.availableChips,
    required this.onChipSelected,
    required this.playerBalance,
    this.selectedChip,
  });

  @override
  State<ChipSelector> createState() => _ChipSelectorState();
}

class _ChipSelectorState extends State<ChipSelector> {
  int? _selectedChip;

  @override
  void initState() {
    super.initState();
    _selectedChip = widget.selectedChip;
  }

  @override
  Widget build(BuildContext context) {
    final availableChips = GameConstants.getAvailableChipDenominations(widget.playerBalance);
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: availableChips.map((chipValue) {
          final isSelected = _selectedChip == chipValue;
          final isAffordable = chipValue <= widget.playerBalance;
          
          return Opacity(
            opacity: isAffordable ? 1.0 : 0.5,
            child: ChipWidget(
              value: chipValue,
              isSelected: isSelected,
              onTap: isAffordable ? () {
                setState(() {
                  _selectedChip = chipValue;
                });
                widget.onChipSelected(chipValue);
              } : null,
            ),
          );
        }).toList(),
      ),
    );
  }
}
