import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/card.dart';
import '../constants/game_constants.dart';

class CardWidget extends StatefulWidget {
  final PlayingCard card;
  final double width;
  final double height;
  final bool showAnimation;
  final bool isDealing;

  const CardWidget({
    super.key,
    required this.card,
    this.width = GameConstants.cardWidth,
    this.height = GameConstants.cardHeight,
    this.showAnimation = false,
    this.isDealing = false,
  });

  @override
  State<CardWidget> createState() => _CardWidgetState();
}

class _CardWidgetState extends State<CardWidget>
    with TickerProviderStateMixin {
  late AnimationController _dealController;
  late AnimationController _flipController;

  late Animation<double> _dealYAnimation;
  late Animation<double> _dealScaleAnimation;
  late Animation<double> _dealOpacityAnimation;
  late Animation<double> _flipAnimation;

  bool _showFront = false;

  @override
  void initState() {
    super.initState();

    // 发牌动画控制器 - 复制原版cardDeal动画
    _dealController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // 翻牌动画控制器 - 简化的翻转效果
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _initializeAnimations();
    _setupAnimationLogic();
  }

  void _initializeAnimations() {
    // 发牌动画 - 更自然的飞入效果
    _dealYAnimation = Tween<double>(
      begin: -80.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _dealController,
      curve: Curves.easeOutCubic,
    ));

    _dealScaleAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dealController,
      curve: Curves.elasticOut,
    ));

    _dealOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dealController,
      curve: Curves.easeOut,
    ));

    // 翻牌动画 - 更真实的3D翻转效果
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOutBack,
    ));
  }

  void _setupAnimationLogic() {
    _flipController.addListener(() {
      // 在翻转到一半时切换显示面
      if (_flipController.value > 0.5 && !_showFront) {
        setState(() {
          _showFront = true;
        });
      }
    });

    // 根据卡牌状态决定动画
    if (widget.isDealing) {
      _startDealAnimation();
    } else if (widget.card.isVisible && widget.showAnimation) {
      // 延迟一点开始翻牌动画，让发牌动画先完成
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted) _startFlipAnimation();
      });
    } else if (widget.card.isVisible) {
      _showFront = true;
      _dealController.value = 1.0;
      _flipController.value = 1.0;
    } else {
      _dealController.value = 1.0;
    }
  }

  Future<void> _startDealAnimation() async {
    await _dealController.forward();
    if (widget.card.isVisible) {
      _startFlipAnimation();
    }
  }

  void _startFlipAnimation() {
    _flipController.forward();
  }

  @override
  void didUpdateWidget(CardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.card.isVisible != oldWidget.card.isVisible && widget.card.isVisible) {
      _startFlipAnimation();
    }
  }

  @override
  void dispose() {
    _dealController.dispose();
    _flipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_dealController, _flipController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _dealYAnimation.value),
          child: Transform.scale(
            scale: _dealScaleAnimation.value,
            child: Opacity(
              opacity: _dealOpacityAnimation.value,
              child: _buildFlipCard(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFlipCard() {
    return AnimatedBuilder(
      animation: _flipAnimation,
      builder: (context, child) {
        final rotationValue = _flipAnimation.value * math.pi;
        final isShowingFront = _flipAnimation.value > 0.5;

        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001) // 透视效果
            ..rotateY(rotationValue),
          child: Container(
            width: widget.width,
            height: widget.height,
            child: isShowingFront
                ? Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.identity()..rotateY(math.pi),
                    child: _buildFrontCard(),
                  )
                : _buildBackCard(),
          ),
        );
      },
    );
  }

  Widget _buildFrontCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFffffff),
            Color(0xFFf8f9fa),
          ],
        ),
        borderRadius: BorderRadius.circular(GameConstants.cardRadius),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(2, 4),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.2),
            blurRadius: 2,
            offset: const Offset(-1, -1),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Top-left corner
          Positioned(
            top: 3,
            left: 3,
            child: _buildCornerContent(),
          ),

          // Center symbol
          Center(
            child: _buildCenterContent(),
          ),

          // Bottom-right corner (rotated)
          Positioned(
            bottom: 3,
            right: 3,
            child: Transform.rotate(
              angle: 3.14159, // 180 degrees
              child: _buildCornerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1e40af),
            Color(0xFF3b82f6),
          ],
        ),
        borderRadius: BorderRadius.circular(GameConstants.cardRadius),
        border: Border.all(
          color: GameConstants.accentGold,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(2, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Pattern overlay
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(GameConstants.cardRadius),
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 0.8,
                colors: [
                  Colors.white.withValues(alpha: 0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),

          // Center icon
          Center(
            child: Container(
              width: widget.width * 0.5,
              height: widget.width * 0.5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: GameConstants.accentGold.withValues(alpha: 0.8),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.casino,
                color: GameConstants.accentGold,
                size: widget.width * 0.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCornerContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.card.valueString,
          style: TextStyle(
            fontSize: widget.width * 0.2,
            fontWeight: FontWeight.bold,
            color: widget.card.isRed ? GameConstants.redSuit : GameConstants.blackSuit,
          ),
        ),
        Text(
          widget.card.suitSymbol,
          style: TextStyle(
            fontSize: widget.width * 0.15,
            color: widget.card.isRed ? GameConstants.redSuit : GameConstants.blackSuit,
          ),
        ),
      ],
    );
  }

  Widget _buildCenterContent() {
    if (_isFaceCard()) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            widget.card.valueString,
            style: TextStyle(
              fontSize: widget.width * 0.35,
              fontWeight: FontWeight.bold,
              color: widget.card.isRed ? GameConstants.redSuit : GameConstants.blackSuit,
              shadows: [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.card.suitSymbol,
            style: TextStyle(
              fontSize: widget.width * 0.25,
              color: widget.card.isRed ? GameConstants.redSuit : GameConstants.blackSuit,
              shadows: [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      return Text(
        widget.card.suitSymbol,
        style: TextStyle(
          fontSize: widget.width * 0.4,
          color: widget.card.isRed ? GameConstants.redSuit : GameConstants.blackSuit,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      );
    }
  }

  bool _isFaceCard() {
    return widget.card.value == CardValue.jack ||
           widget.card.value == CardValue.queen ||
           widget.card.value == CardValue.king ||
           widget.card.value == CardValue.ace;
  }
}

class AnimatedCardWidget extends StatefulWidget {
  final PlayingCard card;
  final double width;
  final double height;
  final Duration animationDuration;
  final VoidCallback? onAnimationComplete;

  const AnimatedCardWidget({
    super.key,
    required this.card,
    this.width = GameConstants.cardWidth,
    this.height = GameConstants.cardHeight,
    this.animationDuration = GameConstants.cardFlipDuration,
    this.onAnimationComplete,
  });

  @override
  State<AnimatedCardWidget> createState() => _AnimatedCardWidgetState();
}

class _AnimatedCardWidgetState extends State<AnimatedCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _flipAnimation;
  bool _showFront = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onAnimationComplete?.call();
      }
    });

    // Start animation if card should be visible
    if (widget.card.isVisible) {
      _flipCard();
    }
  }

  @override
  void didUpdateWidget(AnimatedCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.card.isVisible != oldWidget.card.isVisible) {
      _flipCard();
    }
  }

  void _flipCard() {
    setState(() {
      _showFront = widget.card.isVisible;
    });
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _flipAnimation,
      builder: (context, child) {
        final isShowingFront = _flipAnimation.value > 0.5;
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(_flipAnimation.value * 3.14159),
          child: isShowingFront
              ? CardWidget(
                  card: widget.card,
                  width: widget.width,
                  height: widget.height,
                )
              : Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()..rotateY(3.14159),
                  child: CardWidget(
                    card: widget.card.copyWith(isVisible: false),
                    width: widget.width,
                    height: widget.height,
                  ),
                ),
        );
      },
    );
  }
}
