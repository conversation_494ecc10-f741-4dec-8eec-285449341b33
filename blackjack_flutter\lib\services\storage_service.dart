import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/game_state.dart';
import '../constants/game_constants.dart';

class StorageService {
  static SharedPreferences? _prefs;

  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Balance Management
  Future<void> saveBalance(int balance) async {
    try {
      await _initPrefs();
      await _prefs!.setInt(GameConstants.balanceKey, balance);
      print('💰 Balance saved: \$${balance}');
    } catch (e) {
      print('Failed to save balance: $e');
    }
  }

  Future<int?> getBalance() async {
    try {
      await _initPrefs();
      final balance = _prefs!.getInt(GameConstants.balanceKey);
      if (balance != null) {
        print('💰 Balance loaded: \$${balance}');
      }
      return balance;
    } catch (e) {
      print('Failed to load balance: $e');
      return null;
    }
  }

  // Audio Settings
  Future<void> saveAudioSettings(Map<String, dynamic> settings) async {
    try {
      await _initPrefs();
      
      await _prefs!.setBool(GameConstants.soundEnabledKey, settings['soundEnabled'] ?? true);
      await _prefs!.setBool(GameConstants.musicEnabledKey, settings['musicEnabled'] ?? true);
      await _prefs!.setDouble(GameConstants.soundVolumeKey, settings['soundVolume'] ?? 0.5);
      await _prefs!.setDouble(GameConstants.musicVolumeKey, settings['musicVolume'] ?? 0.3);
      
      print('🔊 Audio settings saved');
    } catch (e) {
      print('Failed to save audio settings: $e');
    }
  }

  Future<Map<String, dynamic>> getAudioSettings() async {
    try {
      await _initPrefs();
      
      return {
        'soundEnabled': _prefs!.getBool(GameConstants.soundEnabledKey) ?? true,
        'musicEnabled': _prefs!.getBool(GameConstants.musicEnabledKey) ?? true,
        'soundVolume': _prefs!.getDouble(GameConstants.soundVolumeKey) ?? 0.5,
        'musicVolume': _prefs!.getDouble(GameConstants.musicVolumeKey) ?? 0.3,
      };
    } catch (e) {
      print('Failed to load audio settings: $e');
      return {
        'soundEnabled': true,
        'musicEnabled': true,
        'soundVolume': 0.5,
        'musicVolume': 0.3,
      };
    }
  }

  // Game Settings
  Future<void> saveGameSettings(GameSettings settings) async {
    try {
      await _initPrefs();
      
      final settingsMap = {
        'deckCount': settings.deckCount,
        'maxPlayers': settings.maxPlayers,
        'minBet': settings.minBet,
        'maxBet': settings.maxBet,
        'allowSurrender': settings.allowSurrender,
        'allowInsurance': settings.allowInsurance,
        'allowDoubleAfterSplit': settings.allowDoubleAfterSplit,
        'dealerHitsSoft17': settings.dealerHitsSoft17,
        'blackjackPayout': settings.blackjackPayout,
      };
      
      await _prefs!.setString(GameConstants.settingsKey, jsonEncode(settingsMap));
      print('⚙️ Game settings saved');
    } catch (e) {
      print('Failed to save game settings: $e');
    }
  }

  Future<GameSettings?> getGameSettings() async {
    try {
      await _initPrefs();
      
      final settingsString = _prefs!.getString(GameConstants.settingsKey);
      if (settingsString == null) return null;
      
      final settingsMap = jsonDecode(settingsString) as Map<String, dynamic>;
      
      return GameSettings(
        deckCount: settingsMap['deckCount'] ?? 6,
        maxPlayers: settingsMap['maxPlayers'] ?? 6,
        minBet: settingsMap['minBet'] ?? 5,
        maxBet: settingsMap['maxBet'] ?? 1000,
        allowSurrender: settingsMap['allowSurrender'] ?? true,
        allowInsurance: settingsMap['allowInsurance'] ?? true,
        allowDoubleAfterSplit: settingsMap['allowDoubleAfterSplit'] ?? true,
        dealerHitsSoft17: settingsMap['dealerHitsSoft17'] ?? true,
        blackjackPayout: settingsMap['blackjackPayout'] ?? 1.5,
      );
    } catch (e) {
      print('Failed to load game settings: $e');
      return null;
    }
  }

  // Game Statistics
  Future<void> saveGameStatistics(GameStatistics stats) async {
    try {
      await _initPrefs();
      
      final statsMap = {
        'gamesPlayed': stats.gamesPlayed,
        'gamesWon': stats.gamesWon,
        'gamesLost': stats.gamesLost,
        'gamesPushed': stats.gamesPushed,
        'blackjacksHit': stats.blackjacksHit,
        'totalWinnings': stats.totalWinnings,
        'totalLosses': stats.totalLosses,
        'currentStreak': stats.currentStreak,
        'bestWinStreak': stats.bestWinStreak,
        'bestLoseStreak': stats.bestLoseStreak,
      };
      
      await _prefs!.setString(GameConstants.statisticsKey, jsonEncode(statsMap));
      print('📊 Game statistics saved');
    } catch (e) {
      print('Failed to save game statistics: $e');
    }
  }

  Future<GameStatistics?> getGameStatistics() async {
    try {
      await _initPrefs();
      
      final statsString = _prefs!.getString(GameConstants.statisticsKey);
      if (statsString == null) return null;
      
      final statsMap = jsonDecode(statsString) as Map<String, dynamic>;
      
      final stats = GameStatistics();
      stats.gamesPlayed = statsMap['gamesPlayed'] ?? 0;
      stats.gamesWon = statsMap['gamesWon'] ?? 0;
      stats.gamesLost = statsMap['gamesLost'] ?? 0;
      stats.gamesPushed = statsMap['gamesPushed'] ?? 0;
      stats.blackjacksHit = statsMap['blackjacksHit'] ?? 0;
      stats.totalWinnings = statsMap['totalWinnings'] ?? 0;
      stats.totalLosses = statsMap['totalLosses'] ?? 0;
      stats.currentStreak = statsMap['currentStreak'] ?? 0;
      stats.bestWinStreak = statsMap['bestWinStreak'] ?? 0;
      stats.bestLoseStreak = statsMap['bestLoseStreak'] ?? 0;
      
      return stats;
    } catch (e) {
      print('Failed to load game statistics: $e');
      return null;
    }
  }

  // Clear all data
  Future<void> clearAllData() async {
    try {
      await _initPrefs();
      await _prefs!.clear();
      print('🗑️ All data cleared');
    } catch (e) {
      print('Failed to clear data: $e');
    }
  }

  // Check if first time user
  Future<bool> isFirstTimeUser() async {
    try {
      await _initPrefs();
      return _prefs!.getBool('first_time_user') ?? true;
    } catch (e) {
      print('Failed to check first time user: $e');
      return true;
    }
  }

  Future<void> setFirstTimeUser(bool isFirstTime) async {
    try {
      await _initPrefs();
      await _prefs!.setBool('first_time_user', isFirstTime);
    } catch (e) {
      print('Failed to set first time user: $e');
    }
  }
}
