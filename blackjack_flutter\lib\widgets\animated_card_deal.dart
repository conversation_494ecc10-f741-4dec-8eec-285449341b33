import 'package:flutter/material.dart';
import '../models/card.dart';
import '../constants/game_constants.dart';
import 'card_widget.dart';

class AnimatedCardDeal extends StatefulWidget {
  final List<PlayingCard> cards;
  final Offset startPosition;
  final List<Offset> endPositions;
  final Duration dealDuration;
  final Duration delayBetweenCards;
  final VoidCallback? onDealComplete;

  const AnimatedCardDeal({
    super.key,
    required this.cards,
    required this.startPosition,
    required this.endPositions,
    this.dealDuration = GameConstants.cardDealDuration,
    this.delayBetweenCards = const Duration(milliseconds: 200),
    this.onDealComplete,
  });

  @override
  State<AnimatedCardDeal> createState() => _AnimatedCardDealState();
}

class _AnimatedCardDealState extends State<AnimatedCardDeal>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<Offset>> _positionAnimations;
  late List<Animation<double>> _rotationAnimations;
  late List<Animation<double>> _scaleAnimations;
  
  int _currentCardIndex = 0;
  bool _dealingComplete = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startDealing();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.cards.length,
      (index) => AnimationController(
        duration: widget.dealDuration,
        vsync: this,
      ),
    );

    _positionAnimations = List.generate(
      widget.cards.length,
      (index) => Tween<Offset>(
        begin: widget.startPosition,
        end: widget.endPositions[index % widget.endPositions.length],
      ).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: Curves.easeOutCubic,
      )),
    );

    _rotationAnimations = List.generate(
      widget.cards.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: (index % 2 == 0 ? 0.1 : -0.1), // Slight rotation variation
      ).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: Curves.easeInOut,
      )),
    );

    _scaleAnimations = List.generate(
      widget.cards.length,
      (index) => Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: Curves.elasticOut,
      )),
    );
  }

  Future<void> _startDealing() async {
    for (int i = 0; i < widget.cards.length; i++) {
      if (mounted) {
        setState(() {
          _currentCardIndex = i;
        });
        
        _controllers[i].forward();
        
        if (i < widget.cards.length - 1) {
          await Future.delayed(widget.delayBetweenCards);
        }
      }
    }
    
    // Wait for the last card animation to complete
    await _controllers.last.forward();
    
    if (mounted) {
      setState(() {
        _dealingComplete = true;
      });
      widget.onDealComplete?.call();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Render cards that have started animating
        for (int i = 0; i <= _currentCardIndex && i < widget.cards.length; i++)
          AnimatedBuilder(
            animation: _controllers[i],
            builder: (context, child) {
              return Positioned(
                left: _positionAnimations[i].value.dx,
                top: _positionAnimations[i].value.dy,
                child: Transform.rotate(
                  angle: _rotationAnimations[i].value,
                  child: Transform.scale(
                    scale: _scaleAnimations[i].value,
                    child: CardWidget(
                      card: widget.cards[i],
                      showAnimation: true,
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}

class CardFlyAnimation extends StatefulWidget {
  final PlayingCard card;
  final Offset startPosition;
  final Offset endPosition;
  final Duration duration;
  final VoidCallback? onComplete;

  const CardFlyAnimation({
    super.key,
    required this.card,
    required this.startPosition,
    required this.endPosition,
    this.duration = const Duration(milliseconds: 800),
    this.onComplete,
  });

  @override
  State<CardFlyAnimation> createState() => _CardFlyAnimationState();
}

class _CardFlyAnimationState extends State<CardFlyAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _positionAnimation = Tween<Offset>(
      begin: widget.startPosition,
      end: widget.endPosition,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutCubic,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 6.28, // Full rotation
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onComplete?.call();
      }
    });

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Positioned(
          left: _positionAnimation.value.dx,
          top: _positionAnimation.value.dy,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: CardWidget(card: widget.card),
            ),
          ),
        );
      },
    );
  }
}

class CardStackAnimation extends StatefulWidget {
  final List<PlayingCard> cards;
  final Offset position;
  final Duration stackDuration;
  final VoidCallback? onStackComplete;

  const CardStackAnimation({
    super.key,
    required this.cards,
    required this.position,
    this.stackDuration = const Duration(milliseconds: 300),
    this.onStackComplete,
  });

  @override
  State<CardStackAnimation> createState() => _CardStackAnimationState();
}

class _CardStackAnimationState extends State<CardStackAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _offsetAnimations;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startStacking();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.cards.length,
      (index) => AnimationController(
        duration: widget.stackDuration,
        vsync: this,
      ),
    );

    _offsetAnimations = List.generate(
      widget.cards.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: index * 2.0, // Stack offset
      ).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: Curves.easeOutBack,
      )),
    );
  }

  Future<void> _startStacking() async {
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].forward();
      await Future.delayed(const Duration(milliseconds: 50));
    }
    
    widget.onStackComplete?.call();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        for (int i = 0; i < widget.cards.length; i++)
          AnimatedBuilder(
            animation: _controllers[i],
            builder: (context, child) {
              return Positioned(
                left: widget.position.dx,
                top: widget.position.dy - _offsetAnimations[i].value,
                child: CardWidget(card: widget.cards[i]),
              );
            },
          ),
      ],
    );
  }
}
