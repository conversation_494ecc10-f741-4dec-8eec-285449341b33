import 'card.dart';
import 'player.dart';

enum GamePhase {
  waiting,
  betting,
  dealing,
  playing,
  dealerTurn,
  settlement,
  gameOver
}

enum GameAction {
  hit,
  stand,
  doubleDown,
  split,
  surrender,
  insurance
}

class GameSettings {
  int deckCount;
  int maxPlayers;
  int minBet;
  int maxBet;
  bool allowSurrender;
  bool allowInsurance;
  bool allowDoubleAfterSplit;
  bool dealerHitsSoft17;
  double blackjackPayout;

  GameSettings({
    this.deckCount = 6,
    this.maxPlayers = 6,
    this.minBet = 5,
    this.maxBet = 1000,
    this.allowSurrender = true,
    this.allowInsurance = true,
    this.allowDoubleAfterSplit = true,
    this.dealerHitsSoft17 = true,
    this.blackjackPayout = 1.5,
  });

  GameSettings copyWith({
    int? deckCount,
    int? maxPlayers,
    int? minBet,
    int? maxBet,
    bool? allowSurrender,
    bool? allowInsurance,
    bool? allowDoubleAfterSplit,
    bool? dealerHitsSoft17,
    double? blackjackPayout,
  }) {
    return GameSettings(
      deckCount: deckCount ?? this.deckCount,
      maxPlayers: maxPlayers ?? this.maxPlayers,
      minBet: minBet ?? this.minBet,
      maxBet: maxBet ?? this.maxBet,
      allowSurrender: allowSurrender ?? this.allowSurrender,
      allowInsurance: allowInsurance ?? this.allowInsurance,
      allowDoubleAfterSplit: allowDoubleAfterSplit ?? this.allowDoubleAfterSplit,
      dealerHitsSoft17: dealerHitsSoft17 ?? this.dealerHitsSoft17,
      blackjackPayout: blackjackPayout ?? this.blackjackPayout,
    );
  }
}

class GameStatistics {
  int gamesPlayed = 0;
  int gamesWon = 0;
  int gamesLost = 0;
  int gamesPushed = 0;
  int blackjacksHit = 0;
  int totalWinnings = 0;
  int totalLosses = 0;
  int currentStreak = 0;
  int bestWinStreak = 0;
  int bestLoseStreak = 0;

  double get winRate => gamesPlayed > 0 ? gamesWon / gamesPlayed : 0.0;
  int get netWinnings => totalWinnings - totalLosses;

  void recordWin(int amount) {
    gamesPlayed++;
    gamesWon++;
    totalWinnings += amount;
    currentStreak = currentStreak >= 0 ? currentStreak + 1 : 1;
    if (currentStreak > bestWinStreak) {
      bestWinStreak = currentStreak;
    }
  }

  void recordLoss(int amount) {
    gamesPlayed++;
    gamesLost++;
    totalLosses += amount;
    currentStreak = currentStreak <= 0 ? currentStreak - 1 : -1;
    if (-currentStreak > bestLoseStreak) {
      bestLoseStreak = -currentStreak;
    }
  }

  void recordPush() {
    gamesPlayed++;
    gamesPushed++;
    currentStreak = 0;
  }

  void recordBlackjack() {
    blackjacksHit++;
  }

  GameStatistics copyWith({
    int? gamesPlayed,
    int? gamesWon,
    int? gamesLost,
    int? gamesPushed,
    int? blackjacksHit,
    int? totalWinnings,
    int? totalLosses,
    int? currentStreak,
    int? bestWinStreak,
    int? bestLoseStreak,
  }) {
    final stats = GameStatistics();
    stats.gamesPlayed = gamesPlayed ?? this.gamesPlayed;
    stats.gamesWon = gamesWon ?? this.gamesWon;
    stats.gamesLost = gamesLost ?? this.gamesLost;
    stats.gamesPushed = gamesPushed ?? this.gamesPushed;
    stats.blackjacksHit = blackjacksHit ?? this.blackjacksHit;
    stats.totalWinnings = totalWinnings ?? this.totalWinnings;
    stats.totalLosses = totalLosses ?? this.totalLosses;
    stats.currentStreak = currentStreak ?? this.currentStreak;
    stats.bestWinStreak = bestWinStreak ?? this.bestWinStreak;
    stats.bestLoseStreak = bestLoseStreak ?? this.bestLoseStreak;
    return stats;
  }
}

class GameState {
  Deck deck = Deck();
  List<Player> players = [];
  Hand dealerHand = Hand();
  int currentPlayerIndex = 0;
  GamePhase phase = GamePhase.waiting;
  GameSettings settings = GameSettings();
  GameStatistics statistics = GameStatistics();
  
  bool isGameInProgress = false;
  bool insuranceOffered = false;
  bool dealerHasBlackjack = false;
  String statusMessage = '';
  
  // Audio settings
  bool soundEnabled = true;
  bool musicEnabled = true;
  double soundVolume = 0.5;
  double musicVolume = 0.3;

  Player? get currentPlayer {
    if (currentPlayerIndex >= 0 && currentPlayerIndex < players.length) {
      return players[currentPlayerIndex];
    }
    return null;
  }

  Player? get humanPlayer {
    try {
      return players.firstWhere((player) => player.isHuman);
    } catch (e) {
      return null;
    }
  }

  List<Player> get aiPlayers {
    return players.where((player) => player.isAI).toList();
  }

  bool get allPlayersFinished {
    return players.every((player) => 
      !player.hasActiveHands || 
      player.hands.every((hand) => 
        hand.isStanding || hand.isBust || hand.hasSurrendered
      )
    );
  }

  bool get dealerShouldHit {
    final score = dealerHand.score;
    if (score < 17) return true;
    if (score == 17 && settings.dealerHitsSoft17 && dealerHand.isSoft) return true;
    return false;
  }

  void nextPlayer() {
    do {
      currentPlayerIndex++;
    } while (currentPlayerIndex < players.length && 
             !players[currentPlayerIndex].hasActiveHands);
  }

  void resetGame() {
    for (Player player in players) {
      player.clearHands();
      player.isActive = false;
    }
    dealerHand.clear();
    currentPlayerIndex = 0;
    phase = GamePhase.waiting;
    isGameInProgress = false;
    insuranceOffered = false;
    dealerHasBlackjack = false;
    statusMessage = '';
  }

  GameState copyWith({
    Deck? deck,
    List<Player>? players,
    Hand? dealerHand,
    int? currentPlayerIndex,
    GamePhase? phase,
    GameSettings? settings,
    GameStatistics? statistics,
    bool? isGameInProgress,
    bool? insuranceOffered,
    bool? dealerHasBlackjack,
    String? statusMessage,
    bool? soundEnabled,
    bool? musicEnabled,
    double? soundVolume,
    double? musicVolume,
  }) {
    final newState = GameState();
    newState.deck = deck ?? this.deck;
    newState.players = players ?? this.players.map((p) => p.copyWith()).toList();
    newState.dealerHand = dealerHand ?? this.dealerHand.copyWith();
    newState.currentPlayerIndex = currentPlayerIndex ?? this.currentPlayerIndex;
    newState.phase = phase ?? this.phase;
    newState.settings = settings ?? this.settings.copyWith();
    newState.statistics = statistics ?? this.statistics.copyWith();
    newState.isGameInProgress = isGameInProgress ?? this.isGameInProgress;
    newState.insuranceOffered = insuranceOffered ?? this.insuranceOffered;
    newState.dealerHasBlackjack = dealerHasBlackjack ?? this.dealerHasBlackjack;
    newState.statusMessage = statusMessage ?? this.statusMessage;
    newState.soundEnabled = soundEnabled ?? this.soundEnabled;
    newState.musicEnabled = musicEnabled ?? this.musicEnabled;
    newState.soundVolume = soundVolume ?? this.soundVolume;
    newState.musicVolume = musicVolume ?? this.musicVolume;
    return newState;
  }
}
