import 'package:flutter/material.dart';

class GameConstants {
  // Game Rules
  static const int defaultDeckCount = 6;
  static const int maxPlayers = 6;
  static const int minBet = 5;
  static const int maxBet = 1000;
  static const int defaultBalance = 1000;
  static const double blackjackPayout = 1.5;
  static const double reshuffleThreshold = 0.25;

  // Chip Denominations
  static const List<int> chipDenominations = [1, 5, 10, 50, 100, 500];

  // Animation Durations
  static const Duration cardDealDuration = Duration(milliseconds: 500);
  static const Duration cardFlipDuration = Duration(milliseconds: 300);
  static const Duration chipAnimationDuration = Duration(milliseconds: 400);
  static const Duration resultDisplayDuration = Duration(milliseconds: 2000);
  static const Duration dealerThinkDuration = Duration(milliseconds: 800);

  // Card Dimensions
  static const double cardWidth = 60.0;
  static const double cardHeight = 84.0;
  static const double cardRadius = 8.0;
  static const double cardSpacing = 15.0;

  // Chip Dimensions
  static const double chipSize = 40.0;
  static const double chipThickness = 4.0;

  // Colors
  static const Color primaryGreen = Color(0xFF1a4b3a);
  static const Color secondaryGreen = Color(0xFF2d5a47);
  static const Color accentGold = Color(0xFFffd700);
  static const Color cardWhite = Color(0xFFffffff);
  static const Color cardBack = Color(0xFF1a4b3a);
  static const Color redSuit = Color(0xFFdc143c);
  static const Color blackSuit = Color(0xFF000000);

  // Chip Colors
  static const Map<int, Color> chipColors = {
    1: Color(0xFFffffff),    // White
    5: Color(0xFFff0000),    // Red
    10: Color(0xFF0000ff),   // Blue
    50: Color(0xFF00ff00),   // Green
    100: Color(0xFF000000),  // Black
    500: Color(0xFF800080),  // Purple
  };

  // Text Styles
  static const TextStyle cardValueStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle scoreStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  static const TextStyle betStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: accentGold,
  );

  static const TextStyle statusStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Colors.white,
  );

  // Layout Constants
  static const double tableRadius = 20.0;
  static const double buttonHeight = 48.0;
  static const double buttonRadius = 24.0;
  static const EdgeInsets defaultPadding = EdgeInsets.all(16.0);
  static const EdgeInsets smallPadding = EdgeInsets.all(8.0);

  // Audio Files
  static const String dealSoundPath = 'assets/audio/deal.mp3';
  static const String winSoundPath = 'assets/audio/win.mp3';
  static const String backgroundMusicPath = 'assets/audio/background.mp3';
  static const String chipSoundPath = 'assets/audio/chip.mp3';
  static const String shuffleSoundPath = 'assets/audio/shuffle.mp3';

  // Image Assets
  static const String cardBackImagePath = 'assets/images/card_back.png';
  static const String tableBackgroundPath = 'assets/images/table_background.jpg';
  static const String chipImagePath = 'assets/images/chip.png';

  // Game Messages
  static const String welcomeMessage = 'Welcome! Place your bet to start playing.';
  static const String placeBetMessage = 'Please place your bet (Min: \$5)';
  static const String dealingMessage = 'Dealing cards...';
  static const String yourTurnMessage = 'Your turn - Hit or Stand?';
  static const String dealerTurnMessage = 'Dealer\'s turn...';
  static const String gameOverMessage = 'Game Over! Your balance is \$0.';
  static const String blackjackMessage = 'Blackjack! You win!';
  static const String bustMessage = 'Bust! You lose.';
  static const String winMessage = 'You win!';
  static const String loseMessage = 'You lose.';
  static const String pushMessage = 'Push - It\'s a tie!';

  // Strategy Hints
  static const Map<String, String> basicStrategyHints = {
    'hit': 'Basic strategy suggests: HIT',
    'stand': 'Basic strategy suggests: STAND',
    'double': 'Basic strategy suggests: DOUBLE DOWN',
    'split': 'Basic strategy suggests: SPLIT',
    'surrender': 'Basic strategy suggests: SURRENDER',
  };

  // Preferences Keys
  static const String soundEnabledKey = 'sound_enabled';
  static const String musicEnabledKey = 'music_enabled';
  static const String soundVolumeKey = 'sound_volume';
  static const String musicVolumeKey = 'music_volume';
  static const String balanceKey = 'player_balance';
  static const String statisticsKey = 'game_statistics';
  static const String settingsKey = 'game_settings';

  // Validation
  static bool isValidBet(int bet, int balance) {
    return bet >= minBet && bet <= maxBet && bet <= balance;
  }

  static List<int> getAvailableChipDenominations(int balance) {
    return chipDenominations.where((chip) => chip <= balance).toList();
  }

  static Color getChipColor(int value) {
    return chipColors[value] ?? Colors.grey;
  }

  static String formatCurrency(int amount) {
    return '\$${amount.toString()}';
  }

  static String formatScore(int score) {
    return score.toString();
  }
}
