import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../constants/game_constants.dart';
import '../models/game_state.dart';
import '../models/player.dart';
import '../widgets/card_widget.dart';
import '../widgets/chip_widget.dart';
import '../widgets/game_controls.dart';
import '../widgets/player_info.dart';
import '../widgets/enhanced_card_animations.dart';
import '../widgets/chip_animation_manager.dart';
import '../widgets/enhanced_deal_animation.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  final GlobalKey _chipTrayKey = GlobalKey();
  final GlobalKey _bettingAreaKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/backImg.webp'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Consumer<GameProvider>(
            builder: (context, gameProvider, child) {
              return ChipAnimationManager(
                chipTrayKey: _chipTrayKey,
                bettingAreaKey: _bettingAreaKey,
                child: Stack(
                  children: [
                    // Background overlay for better contrast
                    Container(
                      color: Colors.black.withValues(alpha: 0.3),
                    ),

                    // Main game layout
                    Column(
                      children: [
                        // Top Status Bar
                        _buildTopBar(context, gameProvider),

                        // Game Table
                        Expanded(
                          child: _buildGameTable(context, gameProvider),
                        ),

                        // Bottom Controls
                        _buildBottomControls(context, gameProvider),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar(BuildContext context, GameProvider gameProvider) {
    final player = gameProvider.humanPlayer;
    return Container(
      padding: GameConstants.defaultPadding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Balance Display
          PlayerInfo(
            balance: player?.balance ?? 0,
            currentBet: player?.currentHand.bet ?? 0,
          ),

          // Current Bet Display - 显眼的当前下注
          if (player != null && player.currentHand.bet > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    GameConstants.accentGold.withValues(alpha: 0.8),
                    GameConstants.accentGold.withValues(alpha: 0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.casino, color: Colors.black, size: 18),
                  const SizedBox(width: 6),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'CURRENT BET',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        GameConstants.formatCurrency(player.currentHand.bet),
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

          // Settings Button
          IconButton(
            onPressed: () => _showSettingsDialog(context, gameProvider),
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameTable(BuildContext context, GameProvider gameProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.2,
          colors: [
            GameConstants.primaryGreen,
            GameConstants.primaryGreen.withValues(alpha: 0.8),
            GameConstants.secondaryGreen,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: GameConstants.accentGold,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(27),
        child: Column(
          children: [
            // Dealer Area
            Expanded(
              flex: 2,
              child: _buildDealerArea(gameProvider),
            ),

            // Status Message
            _buildStatusMessage(gameProvider),

            // Player Area with Betting Circle
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  _buildPlayerArea(gameProvider),
                  _buildBettingArea(gameProvider),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDealerArea(GameProvider gameProvider) {
    return Container(
      padding: GameConstants.defaultPadding,
      child: Row(
        children: [
          // Deck Area (left side)
          Expanded(
            flex: 1,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: GameConstants.cardWidth + 4,
                  height: GameConstants.cardHeight + 4,
                  child: DeckWidget(
                    cardCount: gameProvider.gameState.deck.remainingCards,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Cards: ${gameProvider.gameState.deck.remainingCards}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Dealer Cards Area (center)
          Expanded(
            flex: 2,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Dealer',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // Dealer Cards with enhanced animations
                SizedBox(
                  height: 100,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: gameProvider.dealerHand.cards.asMap().entries.map((entry) {
                      final index = entry.key;
                      final card = entry.value;
                      return Transform.translate(
                        offset: Offset(index * -15.0, 0),
                        child: CardWidget(
                          card: card,
                          showAnimation: true,
                          isDealing: index == gameProvider.dealerHand.cards.length - 1,
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 8),

                // Dealer Score
                if (gameProvider.dealerHand.cards.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: GameConstants.accentGold, width: 1),
                    ),
                    child: Text(
                      'Score: ${gameProvider.dealerHand.score}',
                      style: GameConstants.scoreStyle.copyWith(fontSize: 14),
                    ),
                  ),
              ],
            ),
          ),

          // Empty space for symmetry
          const Expanded(flex: 1, child: SizedBox()),
        ],
      ),
    );
  }

  Widget _buildStatusMessage(GameProvider gameProvider) {
    // 简化状态消息，避免颜色变化导致的闪烁
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Text(
        gameProvider.statusMessage,
        style: GameConstants.statusStyle.copyWith(
          color: Colors.white, // 统一使用白色，避免闪烁
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildPlayerArea(GameProvider gameProvider) {
    final player = gameProvider.humanPlayer;
    if (player == null) {
      return const Center(
        child: Text(
          'No player found',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 20),

          // Player Cards Area with enhanced animations
          SizedBox(
            height: 120,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: player.currentHand.cards.asMap().entries.map((entry) {
                final index = entry.key;
                final card = entry.value;
                return Transform.translate(
                  offset: Offset(index * -15.0, 0), // Overlapping cards
                  child: CardWidget(
                    card: card,
                    showAnimation: true,
                    isDealing: index == player.currentHand.cards.length - 1, // 最新的卡牌显示发牌动画
                  ),
                );
              }).toList(),
            ),
          ),

          const SizedBox(height: 16),

          // Player Score
          if (player.currentHand.cards.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: GameConstants.accentGold, width: 1),
              ),
              child: Text(
                'Score: ${player.currentHand.score}',
                style: GameConstants.scoreStyle.copyWith(
                  color: GameConstants.accentGold,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBettingArea(GameProvider gameProvider) {
    final player = gameProvider.humanPlayer;
    if (player == null) return const SizedBox.shrink();

    final isWaitingForBet = gameProvider.gamePhase == GamePhase.waiting ||
                           gameProvider.gamePhase == GamePhase.betting;

    return Positioned(
      bottom: 20,
      left: 0,
      right: 0,
      child: Center(
        child: PulsingBetArea(
          key: _bettingAreaKey,
          isActive: isWaitingForBet && player.currentHand.bet == 0,
          child: WinningAnimation(
            isWinning: player.currentHand.result == HandResult.win ||
                      player.currentHand.result == HandResult.blackjack,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: GameConstants.accentGold,
                  width: 3,
                ),
                color: Colors.black.withValues(alpha: 0.3),
                boxShadow: [
                  BoxShadow(
                    color: GameConstants.accentGold.withValues(alpha: 0.3),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Betting circle background
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.green.withValues(alpha: 0.9),
                          Colors.green.withValues(alpha: 0.6),
                        ],
                      ),
                    ),
                  ),

                  // Bet chips stack with animation
                  if (player.currentHand.bet > 0)
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: _buildBetChipsStack(player.currentHand.bet),
                    ),

                  // Bet amount display - 更显眼的设计
                  if (player.currentHand.bet > 0)
                    Positioned(
                      bottom: -35,
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              GameConstants.accentGold.withValues(alpha: 0.9),
                              GameConstants.accentGold.withValues(alpha: 0.7),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'BET',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              GameConstants.formatCurrency(player.currentHand.bet),
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBetChipsStack(int totalBet) {
    // Calculate chip breakdown similar to original game
    final chips = _calculateChipBreakdown(totalBet);

    return Stack(
      alignment: Alignment.center,
      children: chips.asMap().entries.map((entry) {
        final index = entry.key;
        final chipValue = entry.value;

        return Transform.translate(
          offset: Offset(index * 2.0, -index * 3.0), // Stack effect
          child: ChipWidget(
            value: chipValue,
            size: 30,
          ),
        );
      }).toList(),
    );
  }

  List<int> _calculateChipBreakdown(int totalBet) {
    final chipValues = [1000, 500, 100, 50, 25, 10, 5];
    final chips = <int>[];
    int remaining = totalBet;

    for (final value in chipValues) {
      while (remaining >= value && chips.length < 5) {
        chips.add(value);
        remaining -= value;
      }
    }

    return chips;
  }

  List<Offset> _calculatePlayerCardPositions(int cardCount) {
    final positions = <Offset>[];
    final centerX = MediaQuery.of(context).size.width / 2;
    final baseY = 300.0; // 玩家卡牌区域Y位置

    for (int i = 0; i < cardCount; i++) {
      final x = centerX - (GameConstants.cardWidth / 2) + (i * -15.0);
      positions.add(Offset(x, baseY));
    }

    return positions;
  }

  List<Offset> _calculateDealerCardPositions(int cardCount) {
    final positions = <Offset>[];
    final centerX = MediaQuery.of(context).size.width / 2;
    final baseY = 100.0; // 庄家卡牌区域Y位置

    for (int i = 0; i < cardCount; i++) {
      final x = centerX - (GameConstants.cardWidth / 2) + (i * -15.0);
      positions.add(Offset(x, baseY));
    }

    return positions;
  }

  Widget _buildBottomControls(BuildContext context, GameProvider gameProvider) {
    return Container(
      padding: GameConstants.defaultPadding,
      child: GameControls(
        gameProvider: gameProvider,
        onBetPlaced: (amount) => gameProvider.placeBet(amount),
        onHit: () => gameProvider.hit(),
        onStand: () => gameProvider.stand(),
        onDoubleDown: () => gameProvider.doubleDown(),
        onSplit: () => gameProvider.split(),
        onSurrender: () => gameProvider.surrender(),
        onNewGame: () => gameProvider.startNewGame(),
        onClearBet: () => gameProvider.clearBet(),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context, GameProvider gameProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: GameConstants.secondaryGreen,
        title: const Text(
          'Settings',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Sound Settings
            SwitchListTile(
              title: const Text(
                'Sound Effects',
                style: TextStyle(color: Colors.white),
              ),
              value: gameProvider.gameState.soundEnabled,
              onChanged: (value) {
                gameProvider.updateSoundSettings(soundEnabled: value);
              },
              activeColor: GameConstants.accentGold,
            ),
            
            SwitchListTile(
              title: const Text(
                'Background Music',
                style: TextStyle(color: Colors.white),
              ),
              value: gameProvider.gameState.musicEnabled,
              onChanged: (value) {
                gameProvider.updateSoundSettings(musicEnabled: value);
              },
              activeColor: GameConstants.accentGold,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Close',
              style: TextStyle(color: GameConstants.accentGold),
            ),
          ),
        ],
      ),
    );
  }
}
