import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/card.dart';
import '../constants/game_constants.dart';
import 'card_widget.dart';

class EnhancedDealAnimation extends StatefulWidget {
  final List<PlayingCard> cards;
  final Offset deckPosition;
  final List<Offset> targetPositions;
  final Duration dealDuration;
  final Duration delayBetweenCards;
  final VoidCallback? onCardDealt;
  final VoidCallback? onComplete;

  const EnhancedDealAnimation({
    super.key,
    required this.cards,
    required this.deckPosition,
    required this.targetPositions,
    this.dealDuration = const Duration(milliseconds: 600),
    this.delayBetweenCards = const Duration(milliseconds: 300),
    this.onCardDealt,
    this.onComplete,
  });

  @override
  State<EnhancedDealAnimation> createState() => _EnhancedDealAnimationState();
}

class _EnhancedDealAnimationState extends State<EnhancedDealAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<Offset>> _positionAnimations;
  late List<Animation<double>> _rotationAnimations;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _opacityAnimations;
  
  int _currentCardIndex = -1;
  bool _isDealing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startDealing();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.cards.length,
      (index) => AnimationController(
        duration: widget.dealDuration,
        vsync: this,
      ),
    );

    _positionAnimations = List.generate(
      widget.cards.length,
      (index) {
        final targetIndex = index % widget.targetPositions.length;
        return Tween<Offset>(
          begin: widget.deckPosition,
          end: widget.targetPositions[targetIndex],
        ).animate(CurvedAnimation(
          parent: _controllers[index],
          curve: Curves.easeOutCubic,
        ));
      },
    );

    _rotationAnimations = List.generate(
      widget.cards.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: (index % 2 == 0 ? 0.05 : -0.05) * math.pi,
      ).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: Curves.easeInOut,
      )),
    );

    _scaleAnimations = List.generate(
      widget.cards.length,
      (index) => TweenSequence<double>([
        TweenSequenceItem(
          tween: Tween<double>(begin: 0.8, end: 1.2),
          weight: 0.3,
        ),
        TweenSequenceItem(
          tween: Tween<double>(begin: 1.2, end: 1.0),
          weight: 0.7,
        ),
      ]).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: Curves.elasticOut,
      )),
    );

    _opacityAnimations = List.generate(
      widget.cards.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _controllers[index],
        curve: const Interval(0.0, 0.3, curve: Curves.easeIn),
      )),
    );
  }

  Future<void> _startDealing() async {
    setState(() {
      _isDealing = true;
    });

    for (int i = 0; i < widget.cards.length; i++) {
      if (mounted) {
        setState(() {
          _currentCardIndex = i;
        });

        _controllers[i].forward();
        widget.onCardDealt?.call();

        if (i < widget.cards.length - 1) {
          await Future.delayed(widget.delayBetweenCards);
        }
      }
    }

    await _controllers.last.forward();

    if (mounted) {
      setState(() {
        _isDealing = false;
      });
      widget.onComplete?.call();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        for (int i = 0; i <= _currentCardIndex && i < widget.cards.length; i++)
          AnimatedBuilder(
            animation: _controllers[i],
            builder: (context, child) {
              return Positioned(
                left: _positionAnimations[i].value.dx - GameConstants.cardWidth / 2,
                top: _positionAnimations[i].value.dy - GameConstants.cardHeight / 2,
                child: Transform.rotate(
                  angle: _rotationAnimations[i].value,
                  child: Transform.scale(
                    scale: _scaleAnimations[i].value,
                    child: Opacity(
                      opacity: _opacityAnimations[i].value,
                      child: CardWidget(
                        card: widget.cards[i],
                        showAnimation: false,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}

class DeckWidget extends StatelessWidget {
  final int cardCount;
  final double width;
  final double height;

  const DeckWidget({
    super.key,
    required this.cardCount,
    this.width = GameConstants.cardWidth,
    this.height = GameConstants.cardHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        for (int i = 0; i < math.min(cardCount, 8); i++)
          Positioned(
            left: i * 0.5,
            top: i * 0.5,
            child: Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF1e40af),
                    Color(0xFF3b82f6),
                  ],
                ),
                borderRadius: BorderRadius.circular(GameConstants.cardRadius),
                border: Border.all(
                  color: GameConstants.accentGold,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: Offset(1, 2),
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  Icons.casino,
                  color: GameConstants.accentGold,
                  size: width * 0.3,
                ),
              ),
            ),
          ),
        if (cardCount > 0)
          Positioned(
            bottom: -5,
            right: -5,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: GameConstants.accentGold,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 2,
                    offset: const Offset(1, 1),
                  ),
                ],
              ),
              child: Text(
                cardCount.toString(),
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
