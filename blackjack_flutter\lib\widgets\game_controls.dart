import 'package:flutter/material.dart';
import '../providers/game_provider.dart';
import '../models/game_state.dart';
import '../constants/game_constants.dart';
import 'chip_widget.dart';
import 'enhanced_chip_tray.dart';
import 'chip_animation_manager.dart';

class GameControls extends StatefulWidget {
  final GameProvider gameProvider;
  final Function(int) onBetPlaced;
  final VoidCallback onHit;
  final VoidCallback onStand;
  final VoidCallback onDoubleDown;
  final VoidCallback onSplit;
  final VoidCallback onSurrender;
  final VoidCallback onNewGame;
  final VoidCallback onClearBet;

  const GameControls({
    super.key,
    required this.gameProvider,
    required this.onBetPlaced,
    required this.onHit,
    required this.onStand,
    required this.onDoubleDown,
    required this.onSplit,
    required this.onSurrender,
    required this.onNewGame,
    required this.onClearBet,
  });

  @override
  State<GameControls> createState() => _GameControlsState();
}

class _GameControlsState extends State<GameControls> {
  int? _selectedChipValue;

  @override
  Widget build(BuildContext context) {
    final gamePhase = widget.gameProvider.gamePhase;
    final player = widget.gameProvider.humanPlayer;
    
    if (player == null) {
      return const SizedBox.shrink();
    }

    switch (gamePhase) {
      case GamePhase.waiting:
      case GamePhase.betting:
        return _buildBettingControls(player);
      case GamePhase.playing:
        return _buildGameplayControls(player);
      case GamePhase.settlement:
        return _buildSettlementControls();
      case GamePhase.gameOver:
        return _buildGameOverControls();
      default:
        return _buildWaitingControls();
    }
  }

  Widget _buildBettingControls(player) {
    return Column(
      children: [
        // Enhanced Chip Tray
        EnhancedChipTray(
          availableChips: _getAvailableChipValues(player.balance),
          playerBalance: player.balance,
          selectedChip: _selectedChipValue,
          onChipSelected: (chipValue) {
            setState(() {
              _selectedChipValue = chipValue;
            });
            widget.onBetPlaced(chipValue);
          },
        ),

        const SizedBox(height: 20),

        // Action Buttons Row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Clear Bet Button
            if (player.currentHand.bet > 0)
              _buildActionButton(
                label: 'Clear Bet',
                icon: Icons.clear,
                onPressed: widget.onClearBet,
                isPrimary: false,
              ),

            // Deal Button
            if (player.currentHand.bet > 0)
              _buildActionButton(
                label: 'Deal Cards',
                icon: Icons.play_arrow,
                onPressed: widget.onNewGame,
                isPrimary: true,
              ),
          ],
        ),
      ],
    );
  }

  List<int> _getAvailableChipValues(int balance) {
    final allChips = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
    return allChips.where((chip) => chip <= balance * 2).take(6).toList();
  }

  Widget _buildGameplayControls(player) {
    final hand = player.currentHand;
    
    return Column(
      children: [
        // Primary Actions
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Hit Button
            _buildActionButton(
              label: 'Hit',
              icon: Icons.add_card,
              onPressed: widget.onHit,
              isPrimary: true,
            ),
            
            // Stand Button
            _buildActionButton(
              label: 'Stand',
              icon: Icons.stop,
              onPressed: widget.onStand,
              isPrimary: true,
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Secondary Actions
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Double Down Button
            if (hand.canDoubleDown && player.balance >= hand.bet)
              _buildActionButton(
                label: 'Double',
                icon: Icons.keyboard_double_arrow_up,
                onPressed: widget.onDoubleDown,
                isPrimary: false,
              ),
            
            // Split Button
            if (hand.canSplit && player.balance >= hand.bet)
              _buildActionButton(
                label: 'Split',
                icon: Icons.call_split,
                onPressed: widget.onSplit,
                isPrimary: false,
              ),
            
            // Surrender Button
            if (widget.gameProvider.gameState.settings.allowSurrender && 
                hand.cards.length == 2)
              _buildActionButton(
                label: 'Surrender',
                icon: Icons.flag,
                onPressed: widget.onSurrender,
                isPrimary: false,
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildSettlementControls() {
    return Column(
      children: [
        const SizedBox(height: 16),
        _buildActionButton(
          label: 'Next Round',
          icon: Icons.refresh,
          onPressed: () {
            widget.gameProvider.resetGame();
          },
          isPrimary: true,
        ),
      ],
    );
  }

  Widget _buildGameOverControls() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red, width: 1),
          ),
          child: const Text(
            'Game Over - Insufficient Balance',
            style: TextStyle(
              color: Colors.red,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        _buildActionButton(
          label: 'Restart Game',
          icon: Icons.restart_alt,
          onPressed: () {
            // Reset player balance and restart
            final player = widget.gameProvider.humanPlayer;
            if (player != null) {
              player.balance = GameConstants.defaultBalance;
              widget.gameProvider.resetGame();
            }
          },
          isPrimary: true,
        ),
      ],
    );
  }

  Widget _buildWaitingControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Text(
        'Please wait...',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isPrimary,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: Material(
        elevation: onPressed != null ? 8 : 2,
        borderRadius: BorderRadius.circular(GameConstants.buttonRadius),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(GameConstants.buttonRadius),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: onPressed != null
                    ? (isPrimary
                        ? [
                            GameConstants.accentGold,
                            GameConstants.accentGold.withValues(alpha: 0.8),
                          ]
                        : [
                            GameConstants.secondaryGreen,
                            GameConstants.primaryGreen,
                          ])
                    : [
                        Colors.grey.shade600,
                        Colors.grey.shade700,
                      ],
              ),
              borderRadius: BorderRadius.circular(GameConstants.buttonRadius),
              border: Border.all(
                color: isPrimary
                    ? Colors.white.withValues(alpha: 0.3)
                    : GameConstants.accentGold.withValues(alpha: 0.5),
                width: 1,
              ),
              boxShadow: onPressed != null ? [
                BoxShadow(
                  color: (isPrimary ? GameConstants.accentGold : GameConstants.secondaryGreen)
                      .withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ] : null,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: isPrimary
                      ? GameConstants.primaryGreen
                      : Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    color: isPrimary
                        ? GameConstants.primaryGreen
                        : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class QuickBetControls extends StatelessWidget {
  final Function(int) onBetPlaced;
  final VoidCallback onClearBet;
  final int playerBalance;
  final int currentBet;

  const QuickBetControls({
    super.key,
    required this.onBetPlaced,
    required this.onClearBet,
    required this.playerBalance,
    required this.currentBet,
  });

  @override
  Widget build(BuildContext context) {
    final quickBetAmounts = [5, 10, 25, 50];
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ...quickBetAmounts.map((amount) {
            final isAffordable = amount <= playerBalance;
            return Opacity(
              opacity: isAffordable ? 1.0 : 0.5,
              child: ElevatedButton(
                onPressed: isAffordable ? () => onBetPlaced(amount) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: GameConstants.accentGold,
                  foregroundColor: GameConstants.primaryGreen,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  minimumSize: const Size(60, 36),
                ),
                child: Text(
                  GameConstants.formatCurrency(amount),
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            );
          }).toList(),
          
          // Clear Bet Button
          if (currentBet > 0)
            ElevatedButton(
              onPressed: onClearBet,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: const Size(60, 36),
              ),
              child: const Text(
                'Clear',
                style: TextStyle(fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }
}
