import 'card.dart';

enum PlayerType { human, ai }

enum HandResult { 
  none,
  win, 
  lose, 
  push, 
  blackjack, 
  bust, 
  surrender 
}

class Hand {
  List<PlayingCard> cards = [];
  int bet = 0;
  bool isActive = true;
  bool isStanding = false;
  bool hasDoubledDown = false;
  bool hasSurrendered = false;
  bool hasInsurance = false;
  int insuranceBet = 0;
  HandResult result = HandResult.none;

  Hand({this.bet = 0});

  void addCard(PlayingCard card) {
    cards.add(card);
  }

  void clear() {
    cards.clear();
    bet = 0;
    isActive = true;
    isStanding = false;
    hasDoubledDown = false;
    hasSurrendered = false;
    hasInsurance = false;
    insuranceBet = 0;
    result = HandResult.none;
  }

  int get score {
    int total = 0;
    int aces = 0;

    for (PlayingCard card in cards) {
      if (card.value == CardValue.ace) {
        aces++;
        total += 11;
      } else {
        total += card.numericValue;
      }
    }

    // Adjust for aces
    while (total > 21 && aces > 0) {
      total -= 10;
      aces--;
    }

    return total;
  }

  bool get isBust => score > 21;
  bool get isBlackjack => score == 21 && cards.length == 2;
  bool get isSoft => cards.any((card) => card.value == CardValue.ace) && score <= 21;
  bool get canSplit => cards.length == 2 && cards[0].value == cards[1].value;
  bool get canDoubleDown => cards.length == 2 && !hasDoubledDown;

  Hand copyWith({
    List<PlayingCard>? cards,
    int? bet,
    bool? isActive,
    bool? isStanding,
    bool? hasDoubledDown,
    bool? hasSurrendered,
    bool? hasInsurance,
    int? insuranceBet,
    HandResult? result,
  }) {
    final newHand = Hand(bet: bet ?? this.bet);
    newHand.cards = cards ?? List.from(this.cards);
    newHand.isActive = isActive ?? this.isActive;
    newHand.isStanding = isStanding ?? this.isStanding;
    newHand.hasDoubledDown = hasDoubledDown ?? this.hasDoubledDown;
    newHand.hasSurrendered = hasSurrendered ?? this.hasSurrendered;
    newHand.hasInsurance = hasInsurance ?? this.hasInsurance;
    newHand.insuranceBet = insuranceBet ?? this.insuranceBet;
    newHand.result = result ?? this.result;
    return newHand;
  }
}

class Player {
  final String id;
  final String name;
  final PlayerType type;
  final String avatarPath;
  
  List<Hand> hands = [Hand()];
  int currentHandIndex = 0;
  int balance = 1000;
  bool isActive = false;

  Player({
    required this.id,
    required this.name,
    required this.type,
    this.avatarPath = '',
    this.balance = 1000,
  });

  Hand get currentHand => hands[currentHandIndex];
  bool get hasMultipleHands => hands.length > 1;
  bool get isHuman => type == PlayerType.human;
  bool get isAI => type == PlayerType.ai;

  void addHand({int bet = 0}) {
    hands.add(Hand(bet: bet));
  }

  void clearHands() {
    hands.clear();
    hands.add(Hand());
    currentHandIndex = 0;
  }

  void nextHand() {
    if (currentHandIndex < hands.length - 1) {
      currentHandIndex++;
    }
  }

  void resetToFirstHand() {
    currentHandIndex = 0;
  }

  bool get hasActiveHands {
    return hands.any((hand) => hand.isActive && !hand.isStanding && !hand.isBust && !hand.hasSurrendered);
  }

  int get totalBet {
    return hands.fold(0, (sum, hand) => sum + hand.bet);
  }

  int get totalInsuranceBet {
    return hands.fold(0, (sum, hand) => sum + hand.insuranceBet);
  }

  void placeBet(int amount, {int handIndex = 0}) {
    if (handIndex < hands.length && balance >= amount) {
      hands[handIndex].bet += amount;
      balance -= amount;
    }
  }

  void winBet(int amount) {
    balance += amount;
  }

  Player copyWith({
    String? id,
    String? name,
    PlayerType? type,
    String? avatarPath,
    List<Hand>? hands,
    int? currentHandIndex,
    int? balance,
    bool? isActive,
  }) {
    final newPlayer = Player(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      avatarPath: avatarPath ?? this.avatarPath,
      balance: balance ?? this.balance,
    );
    newPlayer.hands = hands ?? this.hands.map((hand) => hand.copyWith()).toList();
    newPlayer.currentHandIndex = currentHandIndex ?? this.currentHandIndex;
    newPlayer.isActive = isActive ?? this.isActive;
    return newPlayer;
  }
}
