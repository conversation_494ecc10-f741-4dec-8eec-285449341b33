import 'package:flutter/foundation.dart';
import '../models/game_state.dart';
import '../models/player.dart';
import '../models/card.dart';
import '../constants/game_constants.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';

class GameProvider extends ChangeNotifier {
  GameState _gameState = GameState();
  final AudioService _audioService = AudioService();
  final StorageService _storageService = StorageService();

  GameState get gameState => _gameState;
  
  // Getters for easy access
  List<Player> get players => _gameState.players;
  Player? get currentPlayer => _gameState.currentPlayer;
  Player? get humanPlayer => _gameState.humanPlayer;
  Hand get dealerHand => _gameState.dealerHand;
  GamePhase get gamePhase => _gameState.phase;
  bool get isGameInProgress => _gameState.isGameInProgress;
  String get statusMessage => _gameState.statusMessage;

  GameProvider() {
    _initializeGame();
  }

  Future<void> _initializeGame() async {
    await _loadGameData();
    _setupInitialGame();
    notifyListeners();
  }

  Future<void> _loadGameData() async {
    // Load saved balance
    final savedBalance = await _storageService.getBalance();
    if (savedBalance != null) {
      _gameState.humanPlayer?.balance = savedBalance;
    }

    // Load game settings
    final savedSettings = await _storageService.getGameSettings();
    if (savedSettings != null) {
      _gameState.settings = savedSettings;
    }

    // Load audio settings
    final audioSettings = await _storageService.getAudioSettings();
    _gameState.soundEnabled = audioSettings['soundEnabled'] ?? true;
    _gameState.musicEnabled = audioSettings['musicEnabled'] ?? true;
    _gameState.soundVolume = audioSettings['soundVolume'] ?? 0.5;
    _gameState.musicVolume = audioSettings['musicVolume'] ?? 0.3;
  }

  void _setupInitialGame() {
    // Create deck
    _gameState.deck.createStandardDeck(deckCount: _gameState.settings.deckCount);
    _gameState.deck.shuffle();

    // Create human player
    final humanPlayer = Player(
      id: 'human',
      name: 'You',
      type: PlayerType.human,
      balance: GameConstants.defaultBalance,
    );
    
    _gameState.players = [humanPlayer];
    _gameState.currentPlayerIndex = 0;
    _gameState.phase = GamePhase.waiting;
    _gameState.statusMessage = GameConstants.welcomeMessage;
  }

  // Game Actions
  void placeBet(int amount) {
    final player = _gameState.humanPlayer;
    if (player == null || !GameConstants.isValidBet(amount, player.balance)) {
      return;
    }

    final oldBalance = player.balance;
    player.placeBet(amount);
    print('💸 Bet placed: $amount, Balance: $oldBalance → ${player.balance}');

    _gameState.phase = GamePhase.betting;
    _gameState.statusMessage = 'Bet placed: ${GameConstants.formatCurrency(amount)}';

    _playSound('chip');
    _saveBalance();
    notifyListeners();
  }

  void clearBet() {
    final player = _gameState.humanPlayer;
    if (player == null) return;

    final currentBet = player.currentHand.bet;
    player.balance += currentBet;
    player.currentHand.bet = 0;
    
    _gameState.statusMessage = 'Bet cleared';
    _saveBalance();
    notifyListeners();
  }

  void startNewGame() {
    if (_gameState.humanPlayer?.currentHand.bet == 0) {
      _gameState.statusMessage = GameConstants.placeBetMessage;
      notifyListeners();
      return;
    }

    _gameState.phase = GamePhase.dealing;
    _gameState.isGameInProgress = true;
    _gameState.statusMessage = GameConstants.dealingMessage;
    
    _dealInitialCards();
    notifyListeners();
  }

  void _dealInitialCards() {
    // Clear previous hands
    for (Player player in _gameState.players) {
      player.clearHands();
    }
    _gameState.dealerHand.clear();

    // Deal two cards to each player and dealer
    for (int i = 0; i < 2; i++) {
      // Deal to players
      for (Player player in _gameState.players) {
        final card = _gameState.deck.dealCard();
        if (card != null) {
          player.currentHand.addCard(card);
        }
      }
      
      // Deal to dealer (second card face down)
      final dealerCard = _gameState.deck.dealCard();
      if (dealerCard != null) {
        final visibleCard = i == 0 ? dealerCard : dealerCard.copyWith(isVisible: false);
        _gameState.dealerHand.addCard(visibleCard);
      }
    }

    _playSound('deal');
    
    // Check for blackjacks
    _checkForBlackjacks();
  }

  void _checkForBlackjacks() {
    final humanPlayer = _gameState.humanPlayer;
    if (humanPlayer?.currentHand.isBlackjack == true) {
      if (_gameState.dealerHand.isBlackjack) {
        _endGame(); // Push
      } else {
        _endGame(); // Player blackjack wins
      }
      return;
    }

    if (_gameState.dealerHand.isBlackjack) {
      _endGame(); // Dealer blackjack
      return;
    }

    // Check for insurance
    if (_gameState.dealerHand.cards.first.value == CardValue.ace) {
      _offerInsurance();
    } else {
      _startPlayerTurn();
    }
  }

  void _offerInsurance() {
    _gameState.insuranceOffered = true;
    _gameState.statusMessage = 'Insurance available - Dealer shows Ace';
    notifyListeners();
  }

  void buyInsurance() {
    final player = _gameState.humanPlayer;
    if (player == null) return;

    final insuranceAmount = player.currentHand.bet ~/ 2;
    if (player.balance >= insuranceAmount) {
      player.balance -= insuranceAmount;
      player.currentHand.hasInsurance = true;
      player.currentHand.insuranceBet = insuranceAmount;
      
      _gameState.insuranceOffered = false;
      _saveBalance();
      _startPlayerTurn();
    }
  }

  void declineInsurance() {
    _gameState.insuranceOffered = false;
    _startPlayerTurn();
  }

  void _startPlayerTurn() {
    _gameState.phase = GamePhase.playing;
    _gameState.statusMessage = GameConstants.yourTurnMessage;
    notifyListeners();
  }

  void hit() {
    final player = _gameState.humanPlayer;
    if (player == null || _gameState.phase != GamePhase.playing) return;

    final card = _gameState.deck.dealCard();
    if (card != null) {
      player.currentHand.addCard(card);
      _playSound('deal');

      if (player.currentHand.isBust) {
        _gameState.statusMessage = GameConstants.bustMessage;
        _endGame();
      } else if (player.currentHand.score == 21) {
        stand();
      } else {
        _gameState.statusMessage = GameConstants.yourTurnMessage;
      }
      
      notifyListeners();
    }
  }

  void stand() {
    final player = _gameState.humanPlayer;
    if (player == null) return;

    player.currentHand.isStanding = true;
    _startDealerTurn();
  }

  void doubleDown() {
    final player = _gameState.humanPlayer;
    if (player == null || !player.currentHand.canDoubleDown) return;

    final additionalBet = player.currentHand.bet;
    if (player.balance >= additionalBet) {
      player.balance -= additionalBet;
      player.currentHand.bet += additionalBet;
      player.currentHand.hasDoubledDown = true;
      
      // Deal one more card and stand
      hit();
      if (!player.currentHand.isBust) {
        stand();
      }
      
      _saveBalance();
    }
  }

  void split() {
    final player = _gameState.humanPlayer;
    if (player == null || !player.currentHand.canSplit) return;

    final originalBet = player.currentHand.bet;
    if (player.balance >= originalBet) {
      // Create second hand
      final secondCard = player.currentHand.cards.removeLast();
      player.addHand(bet: originalBet);
      player.hands[1].addCard(secondCard);
      
      player.balance -= originalBet;
      _saveBalance();
      notifyListeners();
    }
  }

  void surrender() {
    final player = _gameState.humanPlayer;
    if (player == null) return;

    player.currentHand.hasSurrendered = true;
    player.balance += player.currentHand.bet ~/ 2; // Return half bet
    
    _gameState.statusMessage = 'Surrendered';
    _saveBalance();
    _endGame();
  }

  void _startDealerTurn() {
    _gameState.phase = GamePhase.dealerTurn;
    _gameState.statusMessage = GameConstants.dealerTurnMessage;

    // Reveal dealer's hidden card
    if (_gameState.dealerHand.cards.length > 1) {
      _gameState.dealerHand.cards[1] = _gameState.dealerHand.cards[1].copyWith(isVisible: true);
    }

    notifyListeners();

    // 添加短暂延迟，让翻牌动画完成，避免UI闪烁
    Future.delayed(const Duration(milliseconds: 500), () {
      _dealerPlay();
    });
  }

  void _dealerPlay() {
    // 批量处理庄家的牌，减少UI更新频率
    final cardsToAdd = <PlayingCard>[];

    while (_gameState.dealerShouldHit) {
      final card = _gameState.deck.dealCard();
      if (card != null) {
        cardsToAdd.add(card);
        _gameState.dealerHand.addCard(card);
      }
    }

    // 只在最后通知一次UI更新
    if (cardsToAdd.isNotEmpty) {
      _playSound('deal');
      notifyListeners();
    }

    _endGame();
  }

  void _endGame() {
    _gameState.phase = GamePhase.settlement;
    _gameState.isGameInProgress = false;
    
    _calculateResults();
    _saveBalance();
    notifyListeners();
  }

  void _calculateResults() {
    final player = _gameState.humanPlayer;
    if (player == null) return;

    final playerHand = player.currentHand;
    final dealerScore = _gameState.dealerHand.score;
    final playerScore = playerHand.score;

    if (playerHand.hasSurrendered) {
      playerHand.result = HandResult.surrender;
      _gameState.statusMessage = 'Surrendered';
    } else if (playerHand.isBust) {
      playerHand.result = HandResult.lose;
      _gameState.statusMessage = GameConstants.bustMessage;
    } else if (_gameState.dealerHand.isBust) {
      playerHand.result = HandResult.win;
      _gameState.statusMessage = GameConstants.winMessage;
      _payoutWin(playerHand);
    } else if (playerHand.isBlackjack && !_gameState.dealerHand.isBlackjack) {
      playerHand.result = HandResult.blackjack;
      _gameState.statusMessage = GameConstants.blackjackMessage;
      _payoutBlackjack(playerHand);
    } else if (playerScore > dealerScore) {
      playerHand.result = HandResult.win;
      _gameState.statusMessage = GameConstants.winMessage;
      _payoutWin(playerHand);
    } else if (playerScore < dealerScore) {
      playerHand.result = HandResult.lose;
      _gameState.statusMessage = GameConstants.loseMessage;
    } else {
      playerHand.result = HandResult.push;
      _gameState.statusMessage = GameConstants.pushMessage;
      _payoutPush(playerHand);
    }

    // Handle insurance
    if (playerHand.hasInsurance) {
      if (_gameState.dealerHand.isBlackjack) {
        player.balance += playerHand.insuranceBet * 2; // Insurance pays 2:1
      }
    }

    _playResultSound(playerHand.result);
  }

  void _payoutWin(Hand hand) {
    final player = _gameState.humanPlayer;
    if (player != null) {
      // 玩家胜利：返还下注金额 + 等额奖金
      player.balance += hand.bet * 2; // Return bet + equal winnings
      print('💰 Win payout: ${hand.bet * 2}, New balance: ${player.balance}');
    }
  }

  void _payoutBlackjack(Hand hand) {
    final player = _gameState.humanPlayer;
    if (player != null) {
      // 21点：返还下注金额 + 1.5倍奖金
      final payout = (hand.bet * 2.5).round(); // bet + 1.5x bet
      player.balance += payout;
      print('🃏 Blackjack payout: $payout, New balance: ${player.balance}');
    }
  }

  void _payoutPush(Hand hand) {
    final player = _gameState.humanPlayer;
    if (player != null) {
      // 平局：只返还下注金额
      player.balance += hand.bet;
      print('🤝 Push payout: ${hand.bet}, New balance: ${player.balance}');
    }
  }

  void _playSound(String soundType) {
    if (_gameState.soundEnabled) {
      _audioService.playSound(soundType, volume: _gameState.soundVolume);
    }
  }

  void _playResultSound(HandResult result) {
    if (!_gameState.soundEnabled) return;
    
    switch (result) {
      case HandResult.win:
      case HandResult.blackjack:
        _audioService.playSound('win', volume: _gameState.soundVolume);
        break;
      default:
        break;
    }
  }

  Future<void> _saveBalance() async {
    final player = _gameState.humanPlayer;
    if (player != null) {
      await _storageService.saveBalance(player.balance);
    }
  }

  // Settings
  void updateSoundSettings({bool? soundEnabled, bool? musicEnabled, double? soundVolume, double? musicVolume}) {
    if (soundEnabled != null) _gameState.soundEnabled = soundEnabled;
    if (musicEnabled != null) _gameState.musicEnabled = musicEnabled;
    if (soundVolume != null) _gameState.soundVolume = soundVolume;
    if (musicVolume != null) _gameState.musicVolume = musicVolume;
    
    _storageService.saveAudioSettings({
      'soundEnabled': _gameState.soundEnabled,
      'musicEnabled': _gameState.musicEnabled,
      'soundVolume': _gameState.soundVolume,
      'musicVolume': _gameState.musicVolume,
    });
    
    notifyListeners();
  }

  void resetGame() {
    _gameState.resetGame();
    _gameState.statusMessage = GameConstants.welcomeMessage;
    notifyListeners();
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
