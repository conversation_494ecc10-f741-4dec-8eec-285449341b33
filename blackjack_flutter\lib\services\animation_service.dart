import 'package:flutter/material.dart';
import '../constants/game_constants.dart';

class AnimationService {
  static final AnimationService _instance = AnimationService._internal();
  factory AnimationService() => _instance;
  AnimationService._internal();

  // Animation presets
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 400);
  static const Duration slowAnimation = Duration(milliseconds: 600);

  // Card dealing positions
  static const Offset deckPosition = Offset(50, 50);
  static const List<Offset> playerPositions = [
    Offset(100, 400), // Player 1
    Offset(200, 400), // Player 2
    Offset(300, 400), // Player 3
  ];
  static const Offset dealerPosition = Offset(200, 100);

  // Create a card flip animation
  Animation<double> createFlipAnimation(AnimationController controller) {
    return Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));
  }

  // Create a card slide animation
  Animation<Offset> createSlideAnimation(
    AnimationController controller,
    Offset start,
    Offset end,
  ) {
    return Tween<Offset>(
      begin: start,
      end: end,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeOutCubic,
    ));
  }

  // Create a bounce animation for chips
  Animation<double> createBounceAnimation(AnimationController controller) {
    return Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.elasticOut,
    ));
  }

  // Create a scale animation
  Animation<double> createScaleAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 1.0,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeOutBack,
    ));
  }

  // Create a rotation animation
  Animation<double> createRotationAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 6.28, // Full rotation
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));
  }

  // Create a fade animation
  Animation<double> createFadeAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 1.0,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));
  }

  // Create a color animation
  Animation<Color?> createColorAnimation(
    AnimationController controller,
    Color begin,
    Color end,
  ) {
    return ColorTween(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));
  }

  // Create a staggered animation sequence
  List<Animation<double>> createStaggeredAnimations(
    AnimationController controller,
    int count, {
    double staggerDelay = 0.1,
  }) {
    return List.generate(count, (index) {
      final start = index * staggerDelay;
      final end = start + (1.0 - start);
      
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Interval(start, end, curve: Curves.easeOut),
      ));
    });
  }

  // Create a path animation for card flying
  Animation<Offset> createPathAnimation(
    AnimationController controller,
    List<Offset> path,
  ) {
    if (path.length < 2) {
      throw ArgumentError('Path must contain at least 2 points');
    }

    return TweenSequence<Offset>(
      List.generate(path.length - 1, (index) {
        return TweenSequenceItem<Offset>(
          tween: Tween<Offset>(
            begin: path[index],
            end: path[index + 1],
          ),
          weight: 1.0,
        );
      }),
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOutCubic,
    ));
  }

  // Create a shake animation
  Animation<double> createShakeAnimation(AnimationController controller) {
    return TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: 10.0),
        weight: 1.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 10.0, end: -10.0),
        weight: 1.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: -10.0, end: 10.0),
        weight: 1.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 10.0, end: 0.0),
        weight: 1.0,
      ),
    ]).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.elasticIn,
    ));
  }

  // Create a pulse animation
  Animation<double> createPulseAnimation(AnimationController controller) {
    return TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.2),
        weight: 1.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0),
        weight: 1.0,
      ),
    ]).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));
  }

  // Get animation duration based on game phase
  Duration getAnimationDuration(String animationType) {
    switch (animationType) {
      case 'card_deal':
        return GameConstants.cardDealDuration;
      case 'card_flip':
        return GameConstants.cardFlipDuration;
      case 'chip_move':
        return GameConstants.chipAnimationDuration;
      case 'result_display':
        return GameConstants.resultDisplayDuration;
      case 'dealer_think':
        return GameConstants.dealerThinkDuration;
      default:
        return normalAnimation;
    }
  }

  // Calculate card positions for fan layout
  List<Offset> calculateFanPositions(
    Offset center,
    int cardCount, {
    double fanAngle = 0.5,
    double cardSpacing = 15.0,
  }) {
    if (cardCount == 0) return [];
    if (cardCount == 1) return [center];

    final List<Offset> positions = [];
    final double totalAngle = fanAngle * (cardCount - 1);
    final double startAngle = -totalAngle / 2;

    for (int i = 0; i < cardCount; i++) {
      final double angle = startAngle + (fanAngle * i);
      final double x = center.dx + (cardSpacing * i * 0.8);
      final double y = center.dy + (cardSpacing * 0.3 * (angle.abs()));
      
      positions.add(Offset(x, y));
    }

    return positions;
  }

  // Calculate positions for card stack
  List<Offset> calculateStackPositions(
    Offset basePosition,
    int cardCount, {
    double stackOffset = 2.0,
  }) {
    return List.generate(cardCount, (index) {
      return Offset(
        basePosition.dx + (index * stackOffset * 0.5),
        basePosition.dy - (index * stackOffset),
      );
    });
  }
}
